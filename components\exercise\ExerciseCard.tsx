'use client'

import React from 'react';
import ContentCard from '@/components/ui/content-card';
import type { Exercise } from '@/data/types';

interface ExerciseCardProps {
  exercise: Exercise;
  index: number;
  lessonTitle?: string;
}

export default function ExerciseCard({ exercise, index, lessonTitle }: ExerciseCardProps) {
  return (
    <ContentCard
      exerciseImageUrl={exercise.exerciseImageUrl}
      solutionImageUrl={exercise.solutionImageUrl}
      index={index}
      contentType="exercise"
      lessonTitle={lessonTitle}
    />
  );
}
