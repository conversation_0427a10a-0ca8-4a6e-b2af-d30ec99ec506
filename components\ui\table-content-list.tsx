'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Eye, Download } from 'lucide-react';
import { ImageViewer } from '@/components/ui/image-viewer';
import { usePdfHandler } from '@/hooks/use-pdf-handler';
import { useIsMobile } from '@/hooks/use-mobile';
import { Exercise } from '@/data/types';
import { DynamicContentHeader } from '@/components/seo/dynamic-content-header';

interface TableContentListProps {
  exercises: Exercise[];
  contentType: 'exercise' | 'homework' | 'summary' | 'exam';
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

const TableContentList = ({
  exercises,
  contentType,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: TableContentListProps) => {
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | undefined>(undefined);
  const [currentAlt, setCurrentAlt] = useState('');
  const isMobile = useIsMobile();

  const { handleDownload } = usePdfHandler();

  // Get content type specific labels
  const getLabels = () => {
    switch (contentType) {
      case 'exercise':
        return {
          title: 'تمرين',
          plural: 'التمارين'
        };
      case 'homework':
        return {
          title: 'نمودج',
          plural: 'الفروض'
        };
      case 'summary':
        return {
          title: 'ملخص',
          plural: 'الملخصات'
        };
      case 'exam':
        return {
          title: 'امتحان',
          plural: 'الامتحانات'
        };
      default:
        return {
          title: 'عنصر',
          plural: 'العناصر'
        };
    }
  };

  const labels = getLabels();



  const openImageViewer = (imageUrl: string | undefined, alt: string) => {
    if (imageUrl) {
      setCurrentImage(imageUrl);
      setCurrentAlt(alt);
      setViewerOpen(true);
    }
  };



  const hasSolution = (exercise: Exercise) => {
    return Boolean(exercise.solutionImageUrl && exercise.solutionImageUrl.trim() !== '');
  };

  // Generate custom filename for download
  const generateCustomFilename = (index: number, isSolution: boolean = false) => {
    const prefix = lessonTitle ? `${lessonTitle} - ` : '';
    const suffix = isSolution ? ' - الحل' : '';
    return `${prefix}${labels.title} ${index + 1}${suffix}.pdf`;
  };

  if (!exercises || exercises.length === 0) {
    return (
      <div className="w-full">
        <DynamicContentHeader
          contentType={contentType}
          lessonTitle={lessonTitle}
          subjectName={subjectName}
          yearName={yearName}
          levelName={levelName}
        />

        <div className="p-4 text-center">
          لا توجد {labels.plural} متاحة لهذا الدرس
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <DynamicContentHeader
        contentType={contentType}
        lessonTitle={lessonTitle}
        subjectName={subjectName}
        yearName={yearName}
        levelName={levelName}
      />

      <div className="rounded-md border" dir="rtl">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right font-bold">العنوان</TableHead>
              <TableHead className="text-right font-bold">الإجراءات</TableHead>
              {contentType !== 'summary' && (
                <TableHead className="text-right font-bold">الحل</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {exercises.map((exercise, index) => (
              <TableRow key={exercise.id}>
                <TableCell className="font-medium text-right">
                  {exercise.title || (lessonTitle ? `${lessonTitle} - ${labels.title} ${index + 1}` : `${labels.title} ${index + 1}`)}
                </TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1 justify-start">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openImageViewer(
                        exercise.exerciseImageUrl,
                        `${labels.title} ${index + 1}`
                      )}
                      disabled={!exercise.exerciseImageUrl}
                      className="flex items-center gap-1 w-full justify-center text-xs"
                      title="عرض"
                    >
                      <Eye className="h-3 w-3" />
                      {!isMobile && "عرض"}
                    </Button>

                    {exercise.exerciseImageUrl && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDownload(exercise.exerciseImageUrl!, generateCustomFilename(index))}
                        className="flex items-center gap-1 w-full justify-center text-xs"
                        title="تحميل"
                      >
                        <Download className="h-3 w-3" />
                        {!isMobile && "تحميل"}
                      </Button>
                    )}
                  </div>
                </TableCell>

                {contentType !== 'summary' && (
                  <TableCell>
                    {hasSolution(exercise) ? (
                      <div className="flex flex-col gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openImageViewer(
                            exercise.solutionImageUrl,
                            `حل ${labels.title} ${index + 1}`
                          )}
                          className="flex items-center gap-1 w-full justify-center text-xs"
                          title="عرض الحل"
                        >
                          <Eye className="h-3 w-3" />
                          {!isMobile && "عرض الحل"}
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownload(exercise.solutionImageUrl!, generateCustomFilename(index, true))}
                          className="flex items-center gap-1 w-full justify-center text-xs"
                          title="تحميل الحل"
                        >
                          <Download className="h-3 w-3" />
                          {!isMobile && "تحميل الحل"}
                        </Button>
                      </div>
                    ) : (
                      <span className="text-muted-foreground text-xs text-center block">
                        لا يوجد حل
                      </span>
                    )}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Image Viewer */}
      <ImageViewer
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        imageUrl={currentImage || ''}
        alt={currentAlt}
      />
    </div>
  );
};

export default TableContentList;
