'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'

// Google Analytics component
export function GoogleAnalytics() {
  const pathname = usePathname()

  useEffect(() => {
    if (!process.env.NEXT_PUBLIC_GA_ID) return

    // Track page view
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_title: document.title,
        page_location: window.location.href,
        page_path: pathname,
        send_page_view: true
      })
    }
  }, [pathname])

  return null
}

// Event tracking functions
export const analytics = {
  // Track page views
  pageView: (url: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_title: title || document.title,
        page_location: window.location.href,
        page_path: url,
        send_page_view: true
      })
    }
  },

  // Track custom events
  event: (action: string, parameters?: Record<string, unknown>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: 'engagement',
        event_label: parameters?.label,
        value: parameters?.value,
        ...parameters
      })
    }
  },

  // Track educational interactions
  trackLessonView: (lessonId: string, lessonTitle: string, subjectName?: string) => {
    analytics.event('lesson_view', {
      event_category: 'education',
      lesson_id: lessonId,
      lesson_title: lessonTitle,
      subject_name: subjectName,
      event_label: `${lessonTitle} - ${subjectName || 'Unknown Subject'}`
    })
  },

  trackExerciseStart: (exerciseId: string, lessonId: string, exerciseType?: string) => {
    analytics.event('exercise_start', {
      event_category: 'education',
      exercise_id: exerciseId,
      lesson_id: lessonId,
      exercise_type: exerciseType || 'general',
      event_label: `Exercise ${exerciseId} in Lesson ${lessonId}`
    })
  },

  trackExerciseComplete: (exerciseId: string, lessonId: string, success: boolean, timeSpent?: number) => {
    analytics.event('exercise_complete', {
      event_category: 'education',
      exercise_id: exerciseId,
      lesson_id: lessonId,
      success: success,
      time_spent: timeSpent,
      event_label: `Exercise ${exerciseId} - ${success ? 'Success' : 'Failed'}`
    })
  },

  trackHomeworkView: (lessonId: string, lessonTitle: string) => {
    analytics.event('homework_view', {
      event_category: 'education',
      lesson_id: lessonId,
      lesson_title: lessonTitle,
      event_label: `Homework - ${lessonTitle}`
    })
  },

  trackSummaryView: (lessonId: string, lessonTitle: string) => {
    analytics.event('summary_view', {
      event_category: 'education',
      lesson_id: lessonId,
      lesson_title: lessonTitle,
      event_label: `Summary - ${lessonTitle}`
    })
  },

  trackExamStart: (lessonId: string, lessonTitle: string) => {
    analytics.event('exam_start', {
      event_category: 'education',
      lesson_id: lessonId,
      lesson_title: lessonTitle,
      event_label: `Exam - ${lessonTitle}`
    })
  },

  trackExamComplete: (lessonId: string, score: number, totalQuestions: number, timeSpent?: number) => {
    analytics.event('exam_complete', {
      event_category: 'education',
      lesson_id: lessonId,
      score: score,
      total_questions: totalQuestions,
      percentage: Math.round((score / totalQuestions) * 100),
      time_spent: timeSpent,
      event_label: `Exam Complete - Score: ${score}/${totalQuestions}`
    })
  },

  trackSearch: (query: string, results: number) => {
    analytics.event('search', {
      event_category: 'engagement',
      search_term: query,
      results_count: results,
      event_label: `Search: ${query} (${results} results)`
    })
  },

  trackDownload: (fileType: string, fileName: string, lessonId?: string) => {
    analytics.event('file_download', {
      event_category: 'engagement',
      file_type: fileType,
      file_name: fileName,
      lesson_id: lessonId,
      event_label: `Download: ${fileName}`
    })
  },

  trackError: (errorType: string, errorMessage: string, page?: string) => {
    analytics.event('error', {
      event_category: 'error',
      error_type: errorType,
      error_message: errorMessage,
      page: page || window.location.pathname,
      event_label: `Error: ${errorType} - ${errorMessage}`
    })
  },

  // Track user engagement
  trackTimeOnPage: (timeSpent: number, page: string) => {
    analytics.event('time_on_page', {
      event_category: 'engagement',
      time_spent: timeSpent,
      page: page,
      event_label: `Time on ${page}: ${timeSpent}s`
    })
  },

  trackScrollDepth: (percentage: number, page: string) => {
    analytics.event('scroll_depth', {
      event_category: 'engagement',
      scroll_percentage: percentage,
      page: page,
      event_label: `Scroll ${percentage}% on ${page}`
    })
  }
}

// Hook for tracking time on page
export function useTimeOnPage() {
  const pathname = usePathname()

  useEffect(() => {
    const startTime = Date.now()

    return () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000)
      if (timeSpent > 5) { // Only track if user spent more than 5 seconds
        analytics.trackTimeOnPage(timeSpent, pathname)
      }
    }
  }, [pathname])
}

// Hook for tracking scroll depth
export function useScrollDepth() {
  const pathname = usePathname()

  useEffect(() => {
    let maxScrollPercentage = 0
    const trackedPercentages = new Set<number>()

    const handleScroll = () => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercentage = Math.round((scrollTop / docHeight) * 100)

      if (scrollPercentage > maxScrollPercentage) {
        maxScrollPercentage = scrollPercentage
      }

      // Track at 25%, 50%, 75%, and 100%
      const milestones = [25, 50, 75, 100]
      milestones.forEach(milestone => {
        if (scrollPercentage >= milestone && !trackedPercentages.has(milestone)) {
          trackedPercentages.add(milestone)
          analytics.trackScrollDepth(milestone, pathname)
        }
      })
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [pathname])
}

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void
  }
}
