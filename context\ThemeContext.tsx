'use client'

import { createContext, useContext, useEffect, ReactNode, useState } from 'react';

// Light theme is now the default for better day mode experience
type Theme = 'light';

interface ThemeContextType {
  theme: Theme;
  isViewerMode: boolean;
  setViewerMode: (isViewer: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  // Always use light theme for day mode
  const theme: Theme = 'light';
  const [isViewerMode, setIsViewerMode] = useState(false);

  useEffect(() => {
    // Apply light theme class to document
    document.documentElement.classList.add('light-mode');
    document.documentElement.classList.remove('dark-mode');
  }, []);

  const setViewerMode = (isViewer: boolean) => {
    setIsViewerMode(isViewer);
  };

  return (
    <ThemeContext.Provider value={{ theme, isViewerMode, setViewerMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
