// نظام تخزين مؤقت متقدم مع ضغط وتحسينات ذكية

interface CacheItem<T> {
  data: T | string
  timestamp: number
  ttl: number
  compressed: boolean
  accessCount: number
  lastAccessed: number
  priority: 'low' | 'medium' | 'high'
  size: number
}

interface CacheStats {
  totalItems: number
  totalSize: number
  hitRate: number
  missRate: number
  compressionRatio: number
  oldestItem: number
  newestItem: number
}

interface CacheConfig {
  maxItems: number
  maxSize: number // بالبايت
  defaultTTL: number
  compressionThreshold: number // الحد الأدنى لحجم البيانات للضغط
  cleanupInterval: number
  enableCompression: boolean
  enableStats: boolean
}

class AdvancedCacheManager {
  private prefix: string
  private config: CacheConfig
  private stats: {
    hits: number
    misses: number
    compressionSaved: number
  }
  private cleanupTimer?: NodeJS.Timeout

  constructor(prefix: string = 'advanced_cache_', config: Partial<CacheConfig> = {}) {
    this.prefix = prefix
    this.config = {
      maxItems: 100,
      maxSize: 50 * 1024 * 1024, // 50MB
      defaultTTL: 30 * 60 * 1000, // 30 دقيقة
      compressionThreshold: 1024, // 1KB
      cleanupInterval: 5 * 60 * 1000, // 5 دقائق
      enableCompression: true,
      enableStats: true,
      ...config
    }
    
    this.stats = {
      hits: 0,
      misses: 0,
      compressionSaved: 0
    }

    this.startCleanupTimer()
  }

  // ضغط البيانات باستخدام LZ-string محاكي
  private compress(data: any): string {
    const jsonString = JSON.stringify(data)
    
    if (!this.config.enableCompression || jsonString.length < this.config.compressionThreshold) {
      return jsonString
    }

    try {
      // ضغط بسيط باستخدام Base64 وإزالة التكرار
      const compressed = btoa(unescape(encodeURIComponent(jsonString)))
      
      if (this.config.enableStats) {
        this.stats.compressionSaved += jsonString.length - compressed.length
      }
      
      return compressed
    } catch (error) {
      console.warn('فشل ضغط البيانات:', error)
      return jsonString
    }
  }

  // إلغاء ضغط البيانات
  private decompress(compressedData: string): any {
    try {
      // محاولة إلغاء الضغط
      const decompressed = decodeURIComponent(escape(atob(compressedData)))
      return JSON.parse(decompressed)
    } catch (error) {
      // إذا فشل، فالبيانات غير مضغوطة
      try {
        return JSON.parse(compressedData)
      } catch (parseError) {
        console.error('فشل في إلغاء ضغط البيانات:', parseError)
        return null
      }
    }
  }

  // حساب حجم البيانات
  private calculateSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size
  }

  // تحديد أولوية البيانات
  private determinePriority(key: string): 'low' | 'medium' | 'high' {
    // أولوية عالية للبيانات المهمة
    if (key.includes('levels') || key.includes('user') || key.includes('auth')) {
      return 'high'
    }
    
    // أولوية متوسطة للمواد والدروس
    if (key.includes('subjects') || key.includes('lessons')) {
      return 'medium'
    }
    
    // أولوية منخفضة للباقي
    return 'low'
  }

  // حفظ البيانات
  set<T>(key: string, data: T, ttl?: number, priority?: 'low' | 'medium' | 'high'): boolean {
    if (!this.isStorageAvailable()) return false

    try {
      const now = Date.now()
      const itemTTL = ttl || this.config.defaultTTL
      const itemPriority = priority || this.determinePriority(key)
      const originalSize = this.calculateSize(data)
      
      const compressedData = this.compress(data)
      const finalSize = new Blob([compressedData]).size

      const cacheItem: CacheItem<T | string> = {
        data: compressedData,
        timestamp: now,
        ttl: itemTTL,
        compressed: this.config.enableCompression && finalSize < originalSize,
        accessCount: 0,
        lastAccessed: now,
        priority: itemPriority,
        size: finalSize
      }

      localStorage.setItem(this.prefix + key, JSON.stringify(cacheItem))
      
      // تنظيف إذا تجاوزنا الحدود
      this.enforceStorageLimits()
      
      return true
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error)
      return false
    }
  }

  // جلب البيانات
  get<T>(key: string): T | null {
    if (!this.isStorageAvailable()) return null

    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) {
        this.stats.misses++
        return null
      }

      const cacheItem: CacheItem<T | string> = JSON.parse(item)
      const now = Date.now()

      // التحقق من انتهاء الصلاحية
      if (now - cacheItem.timestamp > cacheItem.ttl) {
        this.remove(key)
        this.stats.misses++
        return null
      }

      // تحديث إحصائيات الوصول
      cacheItem.accessCount++
      cacheItem.lastAccessed = now
      localStorage.setItem(this.prefix + key, JSON.stringify(cacheItem))

      this.stats.hits++

      // إلغاء ضغط البيانات
      if (cacheItem.compressed) {
        return this.decompress(cacheItem.data as string)
      } else {
        return typeof cacheItem.data === 'string' 
          ? JSON.parse(cacheItem.data) 
          : cacheItem.data as T
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      this.stats.misses++
      return null
    }
  }

  // حذف عنصر
  remove(key: string): boolean {
    if (!this.isStorageAvailable()) return false

    try {
      localStorage.removeItem(this.prefix + key)
      return true
    } catch (error) {
      console.error('خطأ في حذف البيانات:', error)
      return false
    }
  }

  // مسح جميع البيانات
  clear(): boolean {
    if (!this.isStorageAvailable()) return false

    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
      keys.forEach(key => localStorage.removeItem(key))
      return true
    } catch (error) {
      console.error('خطأ في مسح البيانات:', error)
      return false
    }
  }

  // فرض حدود التخزين
  private enforceStorageLimits(): void {
    const items = this.getAllItems()
    
    // ترتيب حسب الأولوية والاستخدام
    const sortedItems = items.sort((a, b) => {
      // أولوية أقل أولاً
      const priorityOrder = { low: 0, medium: 1, high: 2 }
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[a.priority] - priorityOrder[b.priority]
      }
      
      // أقل استخداماً أولاً
      if (a.accessCount !== b.accessCount) {
        return a.accessCount - b.accessCount
      }
      
      // أقدم أولاً
      return a.timestamp - b.timestamp
    })

    // حذف العناصر الزائدة
    while (items.length > this.config.maxItems) {
      const itemToRemove = sortedItems.shift()
      if (itemToRemove) {
        this.remove(itemToRemove.key)
        items.splice(items.indexOf(itemToRemove), 1)
      }
    }

    // حذف العناصر إذا تجاوز الحجم
    let totalSize = items.reduce((sum, item) => sum + item.size, 0)
    while (totalSize > this.config.maxSize && sortedItems.length > 0) {
      const itemToRemove = sortedItems.shift()
      if (itemToRemove) {
        this.remove(itemToRemove.key)
        totalSize -= itemToRemove.size
      }
    }
  }

  // جلب جميع العناصر
  private getAllItems(): Array<CacheItem<any> & { key: string }> {
    if (!this.isStorageAvailable()) return []

    const items: Array<CacheItem<any> & { key: string }> = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key)!)
          items.push({ ...item, key: key.replace(this.prefix, '') })
        } catch (error) {
          console.warn('عنصر تالف في التخزين:', key)
        }
      }
    }
    
    return items
  }

  // تنظيف البيانات المنتهية الصلاحية
  cleanup(): number {
    if (!this.isStorageAvailable()) return 0

    const now = Date.now()
    let removedCount = 0
    
    const items = this.getAllItems()
    items.forEach(item => {
      if (now - item.timestamp > item.ttl) {
        this.remove(item.key)
        removedCount++
      }
    })

    return removedCount
  }

  // بدء مؤقت التنظيف
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    this.cleanupTimer = setInterval(() => {
      const removed = this.cleanup()
      if (removed > 0 && process.env.NODE_ENV === 'development') {
        console.log(`🧹 تم تنظيف ${removed} عنصر منتهي الصلاحية`)
      }
    }, this.config.cleanupInterval)
  }

  // إيقاف مؤقت التنظيف
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  // جلب إحصائيات التخزين
  getStats(): CacheStats {
    const items = this.getAllItems()
    const totalRequests = this.stats.hits + this.stats.misses
    
    return {
      totalItems: items.length,
      totalSize: items.reduce((sum, item) => sum + item.size, 0),
      hitRate: totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0,
      missRate: totalRequests > 0 ? (this.stats.misses / totalRequests) * 100 : 0,
      compressionRatio: this.stats.compressionSaved,
      oldestItem: items.length > 0 ? Math.min(...items.map(item => item.timestamp)) : 0,
      newestItem: items.length > 0 ? Math.max(...items.map(item => item.timestamp)) : 0
    }
  }

  // التحقق من توفر التخزين المحلي
  private isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }
}

// إنشاء مثيل مشترك
export const advancedCache = new AdvancedCacheManager('arab_edu_cache_', {
  maxItems: 150,
  maxSize: 75 * 1024 * 1024, // 75MB
  defaultTTL: 45 * 60 * 1000, // 45 دقيقة
  enableCompression: true,
  enableStats: true
})

// دوال مساعدة
export const cacheUtils = {
  // حفظ مع TTL ذكي حسب نوع البيانات
  setWithSmartTTL: <T>(key: string, data: T): boolean => {
    let ttl = 30 * 60 * 1000 // 30 دقيقة افتراضي
    
    if (key.includes('levels') || key.includes('years')) {
      ttl = 60 * 60 * 1000 // ساعة للمستويات والسنوات
    } else if (key.includes('subjects')) {
      ttl = 45 * 60 * 1000 // 45 دقيقة للمواد
    } else if (key.includes('lessons') || key.includes('exercises')) {
      ttl = 20 * 60 * 1000 // 20 دقيقة للدروس والتمارين
    }
    
    return advancedCache.set(key, data, ttl)
  },

  // جلب مع إعادة تحديث في الخلفية
  getWithBackgroundRefresh: async <T>(
    key: string, 
    fetchFunction: () => Promise<T>
  ): Promise<T | null> => {
    const cached = advancedCache.get<T>(key)
    
    if (cached) {
      // إعادة تحديث في الخلفية إذا كانت البيانات قديمة
      setTimeout(async () => {
        try {
          const fresh = await fetchFunction()
          cacheUtils.setWithSmartTTL(key, fresh)
        } catch (error) {
          console.warn('فشل في إعادة التحديث في الخلفية:', error)
        }
      }, 0)
      
      return cached
    }
    
    // جلب البيانات الجديدة
    try {
      const fresh = await fetchFunction()
      cacheUtils.setWithSmartTTL(key, fresh)
      return fresh
    } catch (error) {
      console.error('فشل في جلب البيانات:', error)
      return null
    }
  },

  // جلب الإحصائيات
  getStats: () => advancedCache.getStats(),

  // تنظيف يدوي
  cleanup: () => advancedCache.cleanup(),

  // مسح الكل
  clearAll: () => advancedCache.clear()
}

export default advancedCache
