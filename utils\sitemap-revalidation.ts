/**
 * دوال مساعدة لتحديث sitemap.xml عند تغيير البيانات
 */

import { siteConfig } from '@/lib/config'

/**
 * تحديث sitemap.xml عبر API
 */
export async function revalidateSitemap(): Promise<boolean> {
  try {
    const baseUrl = typeof window !== 'undefined' 
      ? window.location.origin 
      : siteConfig.url

    const response = await fetch(`${baseUrl}/api/revalidate-sitemap`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // إضافة token إذا كان موجود في متغيرات البيئة
        ...(process.env.REVALIDATE_TOKEN && {
          'Authorization': `Bearer ${process.env.REVALIDATE_TOKEN}`
        })
      }
    })

    if (response.ok) {
      const result = await response.json()
      console.log('✅ تم تحديث sitemap.xml:', result.message)
      return true
    } else {
      console.error('❌ فشل تحديث sitemap.xml:', response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ خطأ في تحديث sitemap.xml:', error)
    return false
  }
}

/**
 * تحديث sitemap.xml عند إضافة بيانات جديدة
 * يمكن استدعاؤها بعد إضافة سنة/مادة/درس جديد
 */
export async function triggerSitemapUpdate(
  action: 'add' | 'update' | 'delete',
  type: 'level' | 'year' | 'subject' | 'lesson',
  itemId?: string
): Promise<void> {
  console.log(`🔄 تحديث sitemap.xml بعد ${action} ${type}${itemId ? ` (${itemId})` : ''}`)
  
  // تأخير قصير للتأكد من حفظ البيانات في قاعدة البيانات
  setTimeout(async () => {
    const success = await revalidateSitemap()
    if (success) {
      console.log(`✅ تم تحديث sitemap.xml بنجاح بعد ${action} ${type}`)
    }
  }, 1000) // تأخير ثانية واحدة
}

/**
 * جدولة تحديث sitemap.xml بشكل دوري (للاستخدام في الخلفية)
 */
export function scheduleSitemapRevalidation(intervalMinutes: number = 60): NodeJS.Timeout {
  console.log(`📅 جدولة تحديث sitemap.xml كل ${intervalMinutes} دقيقة`)
  
  return setInterval(async () => {
    console.log('🔄 تحديث sitemap.xml المجدول...')
    await revalidateSitemap()
  }, intervalMinutes * 60 * 1000)
}
