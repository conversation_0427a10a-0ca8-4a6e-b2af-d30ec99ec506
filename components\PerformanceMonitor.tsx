'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

// Simple Badge component since it's not available
const Badge = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${className}`}>
    {children}
  </span>
)
import { Activity, Clock, Database, Zap } from 'lucide-react'

interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

interface PerformanceMetrics {
  navigation?: PerformanceNavigationTiming
  paint?: PerformanceEntry[]
  resources?: PerformanceResourceTiming[]
  memory?: MemoryInfo
}

interface WebVitals {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})
  const [webVitals, setWebVitals] = useState<WebVitals>({})
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return

    const collectMetrics = () => {
      if (typeof window === 'undefined' || !window.performance) return

      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      const memory = (performance as unknown as { memory?: MemoryInfo }).memory

      setMetrics({
        navigation,
        paint,
        resources,
        memory
      })

      // Calculate Web Vitals
      const vitals: WebVitals = {}

      // First Contentful Paint
      const fcpEntry = paint.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) vitals.fcp = fcpEntry.startTime

      // Time to First Byte
      if (navigation) {
        vitals.ttfb = navigation.responseStart - navigation.requestStart
      }

      // Largest Contentful Paint (requires observer)
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number }
            if (lastEntry) {
              setWebVitals(prev => ({ ...prev, lcp: lastEntry.startTime }))
            }
          })
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: PerformanceEntry & { processingStart?: number }) => {
              if (entry.processingStart) {
                setWebVitals(prev => ({ ...prev, fid: entry.processingStart! - entry.startTime }))
              }
            })
          })
          fidObserver.observe({ entryTypes: ['first-input'] })

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0
            list.getEntries().forEach((entry: PerformanceEntry & { hadRecentInput?: boolean; value?: number }) => {
              if (!entry.hadRecentInput && entry.value) {
                clsValue += entry.value
              }
            })
            setWebVitals(prev => ({ ...prev, cls: clsValue }))
          })
          clsObserver.observe({ entryTypes: ['layout-shift'] })
        } catch (e) {
          console.warn('Performance Observer not supported:', e)
        }
      }

      setWebVitals(prev => ({ ...prev, ...vitals }))
    }

    // Collect metrics after page load
    if (document.readyState === 'complete') {
      collectMetrics()
    } else {
      window.addEventListener('load', collectMetrics)
    }

    return () => {
      window.removeEventListener('load', collectMetrics)
    }
  }, [])

  const formatTime = (time?: number) => {
    if (!time) return 'N/A'
    return `${Math.round(time)}ms`
  }

  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'N/A'
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)}MB`
  }

  const getPerformanceScore = (metric: number, thresholds: [number, number]) => {
    if (metric <= thresholds[0]) return 'good'
    if (metric <= thresholds[1]) return 'needs-improvement'
    return 'poor'
  }

  const getScoreColor = (score: string) => {
    switch (score) {
      case 'good': return 'bg-green-500'
      case 'needs-improvement': return 'bg-yellow-500'
      case 'poor': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
        size="sm"
        variant="outline"
      >
        <Activity className="w-4 h-4 mr-2" />
        Performance
      </Button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Performance Monitor</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Web Vitals */}
          <div>
            <h4 className="text-xs font-semibold mb-2 flex items-center">
              <Zap className="w-3 h-3 mr-1" />
              Core Web Vitals
            </h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center justify-between">
                <span>FCP:</span>
                <Badge className={getScoreColor(getPerformanceScore(webVitals.fcp || 0, [1800, 3000]))}>
                  {formatTime(webVitals.fcp)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>LCP:</span>
                <Badge className={getScoreColor(getPerformanceScore(webVitals.lcp || 0, [2500, 4000]))}>
                  {formatTime(webVitals.lcp)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>FID:</span>
                <Badge className={getScoreColor(getPerformanceScore(webVitals.fid || 0, [100, 300]))}>
                  {formatTime(webVitals.fid)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>CLS:</span>
                <Badge className={getScoreColor(getPerformanceScore(webVitals.cls || 0, [0.1, 0.25]))}>
                  {webVitals.cls?.toFixed(3) || 'N/A'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Navigation Timing */}
          {metrics.navigation && (
            <div>
              <h4 className="text-xs font-semibold mb-2 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                Navigation Timing
              </h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>DNS:</span>
                  <span>{formatTime(metrics.navigation.domainLookupEnd - metrics.navigation.domainLookupStart)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Connect:</span>
                  <span>{formatTime(metrics.navigation.connectEnd - metrics.navigation.connectStart)}</span>
                </div>
                <div className="flex justify-between">
                  <span>TTFB:</span>
                  <span>{formatTime(webVitals.ttfb)}</span>
                </div>
                <div className="flex justify-between">
                  <span>DOM Load:</span>
                  <span>{formatTime(metrics.navigation.domContentLoadedEventEnd - metrics.navigation.fetchStart)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Memory Usage */}
          {metrics.memory && (
            <div>
              <h4 className="text-xs font-semibold mb-2 flex items-center">
                <Database className="w-3 h-3 mr-1" />
                Memory Usage
              </h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Used:</span>
                  <span>{formatBytes(metrics.memory.usedJSHeapSize)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total:</span>
                  <span>{formatBytes(metrics.memory.totalJSHeapSize)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Limit:</span>
                  <span>{formatBytes(metrics.memory.jsHeapSizeLimit)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Resource Count */}
          {metrics.resources && (
            <div>
              <h4 className="text-xs font-semibold mb-2">Resources</h4>
              <div className="text-xs">
                <span>{metrics.resources.length} resources loaded</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default PerformanceMonitor
