'use client';

import { useMemo } from 'react';

interface DynamicContentHeaderProps {
  contentType: 'exercise' | 'homework' | 'summary' | 'exam';
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

export const DynamicContentHeader = ({
  contentType,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: DynamicContentHeaderProps) => {

  const contentData = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const academicYear = `${currentYear}-${currentYear + 1}`;

    const baseContent = {
      exercise: {
        title: `تمارين ${lessonTitle || 'الدرس'}`,
        description: `تمارين محلولة ومصححة`,
        action: 'حل وتطبيق',
        icon: '📝',
        keywords: [
          'تمارين', 'حلول', 'تصحيح', 'تطبيقات', 'دروس', 'مسائل', 'أنشطة', 'تدريبات',
          'تمارين المغرب', 'تعليم مغربي', 'مدارس المغرب', 'تلاميذ', 'باك مغربي', 'امتحانات المغرب',
          'دروس مغربية', 'تعليم ثانوي', 'تعليم إعدادي', 'تعليم ابتدائي', 'وزارة التربية الوطنية'
        ]
      },
      homework: {
        title: ` ${lessonTitle || 'الدرس'}`,
        description: `فروض منزلية محلولة`,
        action: 'إنجاز وحل',
        icon: '📚',
        keywords: [
          'فروض', 'فروض منزلية', 'تمارين منزلية', 'أنشطة', 'مراجعة', 'تطبيقات',
          'فروض المغرب', 'فروض محروسة', 'تقييم مستمر', 'مدارس المغرب', 'تلاميذ',
          'تعليم مغربي', 'منهاج مغربي', 'وزارة التربية الوطنية', 'أكاديمية التعليم'
        ]
      },
      summary: {
        title: `ملخصات ${lessonTitle || 'الدرس'}`,
        description: `ملخصات شاملة ومركزة`,
        action: 'مراجعة ودراسة',
        icon: '📄',
        keywords: [
          'ملخصات', 'مراجعة', 'دروس', 'خلاصة', 'تلخيص', 'قواعد', 'نظريات', 'مفاهيم',
          'ملخصات المغرب', 'مراجعة باك', 'تحضير امتحانات', 'مدارس المغرب', 'تلاميذ',
          'تعليم مغربي', 'منهاج مغربي', 'دروس مغربية', 'وزارة التربية الوطنية'
        ]
      },
      exam: {
        title: `امتحانات ${lessonTitle || 'الدرس'}`,
        description: `امتحانات وطنية وجهوية محلولة`,
        action: 'تحضير واختبار',
        icon: '🎯',
        keywords: [
          'امتحانات', 'امتحانات وطنية', 'امتحانات جهوية', 'اختبارات', 'فروض محروسة', 'تقييم', 'نماذج', 'باك',
          'باك مغربي', 'امتحانات المغرب', 'بكالوريا المغرب', 'مدارس المغرب', 'تلاميذ',
          'تعليم ثانوي', 'وزارة التربية الوطنية', 'أكاديمية التعليم', 'امتحانات جهوية المغرب'
        ]
      }
    };

    const content = baseContent[contentType];

    // Generate focused content based on type (under 300 words)
    const generateFocusedText = () => {
      const baseText = `${content.description} ${subjectName ? `في مادة ${subjectName}` : ''} ${yearName ? `للسنة ${yearName}` : ''} ${levelName ? `- ${levelName}` : ''}`;

      switch (contentType) {
        case 'exercise':
          return `${baseText}. مجموعة شاملة من التمارين المحلولة والمصححة المصممة خصيصاً لتلاميذ مدارس المغرب. تتضمن تمارين متدرجة الصعوبة مع حلول مفصلة وشروحات واضحة تساعد على فهم المفاهيم وتطوير المهارات الأساسية.

تغطي التمارين جميع محاور المنهاج المغربي وتتماشى مع معايير وزارة التربية الوطنية. كل تمرين مرفق بحل مفصل يوضح الخطوات والطرق المختلفة للوصول إلى الإجابة الصحيحة، مما يساعد التلاميذ على التحضير الفعال للامتحانات والفروض المحروسة.

هذه التمارين مفيدة للمراجعة اليومية وتقوية الفهم، وتناسب جميع مستويات التلاميذ من المبتدئين إلى المتقدمين. تم إعدادها بعناية لتحاكي أسلوب الأسئلة في الامتحانات الوطنية والجهوية المغربية.`;

        case 'homework':
          return `${baseText}. مجموعة متنوعة من الفروض المنزلية المحلولة المصممة لتلاميذ مدارس المغرب. تساعد هذه الفروض على المراجعة المستمرة والتحضير الفعال للامتحانات والتقييم المستمر.

تتضمن الفروض أسئلة متنوعة تغطي جميع محاور المنهاج المغربي وتتماشى مع معايير وزارة التربية الوطنية. كل فرض مرفق بحلول مفصلة وتصحيح شامل يوضح الأخطاء الشائعة وطرق تجنبها.

هذه الفروض تحاكي نمط الامتحانات الرسمية والفروض المحروسة، مما يساعد التلاميذ على التعود على أسلوب الأسئلة المطلوبة. تتضمن أسئلة موضوعية ومقالية واختيار من متعدد، وتوفر تدريباً شاملاً للتحضير للباك المغربي والامتحانات الجهوية.`;

        case 'summary':
          return `${baseText}. ملخصات مركزة وشاملة تغطي جميع النقاط المهمة والمفاهيم الأساسية المطلوبة لتلاميذ مدارس المغرب. تم إعدادها وفقاً للمنهاج المغربي ومعايير وزارة التربية الوطنية.

تتميز هذه الملخصات بالوضوح والتنظيم المنطقي، حيث تبدأ بالمفاهيم العامة وتتدرج إلى التفاصيل المهمة. تتضمن جداول ومخططات توضيحية تساعد على الفهم السريع والمراجعة الفعالة.

هذه الملخصات مثالية للتحضير للامتحانات الوطنية والجهوية، وتوفر مراجعة سريعة وشاملة للمادة. تساعد التلاميذ على تنظيم المعلومات وربط المفاهيم المختلفة، مما يسهل عملية الحفظ والفهم قبل الامتحانات.`;

        case 'exam':
          return `${baseText}. مجموعة شاملة من نماذج الامتحانات الوطنية والجهوية المغربية مع التصحيح المفصل. تتضمن امتحانات من مختلف الأكاديميات والجهات التعليمية في المغرب للسنوات السابقة.

تساعد هذه النماذج تلاميذ مدارس المغرب على التحضير الفعال للباك المغربي والامتحانات الجهوية. كل امتحان مرفق بتصحيح مفصل وشروحات واضحة تساعد على فهم طريقة الإجابة الصحيحة ومعايير التنقيط.

تتنوع النماذج لتشمل امتحانات الدورة العادية والاستدراكية، مما يوفر تدريباً شاملاً على أنماط الأسئلة المختلفة. تتماشى مع معايير وزارة التربية الوطنية وتحاكي الامتحانات الرسمية بدقة.`;

        default:
          return baseText;
      }
    };

    return {
      ...content,
      fullDescription: `${content.description} في ${subjectName || 'المادة'} للسنة الدراسية ${academicYear}`,
      fullText: generateFocusedText(),
      structuredData: {
        '@context': 'https://schema.org',
        '@type': 'EducationalResource',
        name: content.title,
        description: `${content.description} في ${subjectName || 'المادة'} - منهاج مغربي`,
        educationalLevel: levelName,
        about: {
          '@type': 'Course',
          name: subjectName,
          courseCode: yearName,
          provider: {
            '@type': 'Organization',
            name: 'وزارة التربية الوطنية والتعليم الأولي والرياضة - المملكة المغربية'
          }
        },
        keywords: content.keywords.join(', '),
        inLanguage: 'ar',
        dateModified: new Date().toISOString(),
        publisher: {
          '@type': 'Organization',
          name: 'منصة التعليم المغربي',
          url: 'https://tolabi.net',
          sameAs: [
            'https://www.men.gov.ma'
          ]
        },
        audience: {
          '@type': 'EducationalAudience',
          educationalRole: 'student',
          audienceType: 'تلاميذ مدارس المغرب'
        },
        educationalUse: content.action,
        typicalAgeRange: levelName === 'التعليم الثانوي' ? '15-18' : levelName === 'التعليم الإعدادي' ? '12-15' : '6-12',
        isAccessibleForFree: true,
        learningResourceType: contentType === 'exercise' ? 'تمارين' : contentType === 'homework' ? 'فروض' : contentType === 'summary' ? 'ملخصات' : 'امتحانات'
      }
    };
  }, [contentType, lessonTitle, subjectName, yearName, levelName]);

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(contentData.structuredData)
        }}
      />

      {/* Dynamic Content Header */}
      <div className="mb-6 p-6 bg-gradient-to-r from-green-50 to-yellow-50 dark:from-green-900/20 dark:to-yellow-900/20 rounded-lg border border-green-200 dark:border-green-700" dir="rtl">
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl">{contentData.icon}</span>
            <h2 className="text-xl font-bold text-green-700 dark:text-green-300">
              {contentData.title}
            </h2>
          </div>
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-justify">
            {contentData.fullText}
          </p>
        </div>

        {/* Educational Context Tags */}
        <div className="flex flex-wrap gap-2 text-sm">
          {subjectName && (
            <span className="px-3 py-1 bg-green-600 text-white rounded-full font-medium">
              {subjectName}
            </span>
          )}
          {yearName && (
            <span className="px-3 py-1 bg-yellow-600 text-white rounded-full font-medium">
              {yearName}
            </span>
          )}
          {levelName && (
            <span className="px-3 py-1 bg-green-500 text-white rounded-full font-medium">
              {levelName}
            </span>
          )}
          <span className="px-3 py-1 bg-yellow-500 text-white rounded-full font-medium">
            منهاج مغربي
          </span>
        </div>

        {/* Keywords for SEO (hidden visually but present for SEO) */}
        <div className="sr-only">
          <span>الكلمات المفتاحية: </span>
          {contentData.keywords.join(' • ')}
          <span> • تعليم مغربي • مدارس المغرب • تلاميذ • وزارة التربية الوطنية</span>
        </div>
      </div>
    </>
  );
};

export default DynamicContentHeader;
