'use client';

import TableContentList from '@/components/ui/table-content-list';
import { Exercise } from '@/data/types';

interface SummaryListProps {
  exercises: Exercise[];
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

const SummaryList = ({
  exercises,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: SummaryListProps) => {
  return (
    <TableContentList
      exercises={exercises}
      contentType="summary"
      lessonTitle={lessonTitle}
      subjectName={subjectName}
      yearName={yearName}
      levelName={levelName}
    />
  );
};

export default SummaryList;
