'use client';

import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import type { Year, Subject, Level } from '@/data/types';

interface SubjectBreadcrumbProps {
  subject: Subject;
  year: Year | null;
  level?: Level | null;
}

const SubjectBreadcrumb = ({ subject, year, level }: SubjectBreadcrumbProps) => {
  return (
    <div className="flex justify-between items-center mb-4">
      <div className="flex items-center gap-2 text-sm">
        <Link href="/levels" className="text-muted-foreground hover:text-primary">
          الرئيسية
        </Link>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
        {level && (
          <>
            <span className="text-muted-foreground">{level.name}</span>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </>
        )}
        <Link href={`/year/${year?.id}`} className="text-muted-foreground hover:text-primary">
          {year?.name || ''}
        </Link>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
        <span className="text-primary">{subject.name}</span>
      </div>
    </div>
  );
};

export default SubjectBreadcrumb;
