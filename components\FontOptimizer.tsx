'use client'

import { useEffect, useCallback } from 'react'

interface FontConfig {
  family: string
  weights: number[]
  display: 'auto' | 'block' | 'swap' | 'fallback' | 'optional'
  preload?: boolean
}

const ARABIC_FONTS: FontConfig[] = [
  {
    family: 'Cairo',
    weights: [400, 500, 600, 700],
    display: 'swap',
    preload: true
  },
  {
    family: 'Noto Sans Arabic',
    weights: [400, 500, 600],
    display: 'swap',
    preload: false
  }
]

export function FontOptimizer() {
  const optimizeFonts = useCallback(() => {
    // تحسين تحميل الخطوط الموجودة
    const existingFonts = document.querySelectorAll('link[rel="stylesheet"]')
    existingFonts.forEach(link => {
      const href = link.getAttribute('href')
      if (href && href.includes('fonts.googleapis.com')) {
        // إضافة display=swap للخطوط الموجودة
        if (!href.includes('display=')) {
          const separator = href.includes('?') ? '&' : '?'
          link.setAttribute('href', `${href}${separator}display=swap`)
        }
      }
    })
  }, [])

  const generateGoogleFontUrl = useCallback((family: string, weights: number[]): string => {
    const baseUrl = 'https://fonts.googleapis.com/css2'
    const params = new URLSearchParams({
      family: `${family}:wght@${weights.join(';')}`,
      display: 'swap',
      subset: 'arabic'
    })
    return `${baseUrl}?${params.toString()}`
  }, [])

  const preloadCriticalFonts = useCallback(() => {
    ARABIC_FONTS.forEach(font => {
      if (font.preload) {
        font.weights.forEach(weight => {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.as = 'font'
          link.type = 'font/woff2'
          link.crossOrigin = 'anonymous'
          link.href = generateGoogleFontUrl(font.family, [weight])

          // إضافة الرابط إلى head إذا لم يكن موجوداً
          if (!document.querySelector(`link[href="${link.href}"]`)) {
            document.head.appendChild(link)
          }
        })
      }
    })
  }, [generateGoogleFontUrl])

  const setupFontLoadingStrategy = useCallback(() => {
    // استخدام Font Loading API إذا كان متاحاً
    if ('fonts' in document) {
      const fontFaces: FontFace[] = []

      ARABIC_FONTS.forEach(font => {
        font.weights.forEach(weight => {
          const fontUrl = generateGoogleFontUrl(font.family, [weight])
          const fontFace = new FontFace(
            font.family,
            `url(${fontUrl})`,
            {
              weight: weight.toString(),
              display: font.display,
              unicodeRange: 'U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC'
            }
          )

          fontFaces.push(fontFace)
          document.fonts.add(fontFace)
        })
      })

      // تحميل الخطوط بشكل غير متزامن
      Promise.all(fontFaces.map(font => font.load()))
        .then(() => {
          console.log('✅ تم تحميل جميع الخطوط بنجاح')
          document.body.classList.add('fonts-loaded')
        })
        .catch(() => {
          console.warn('⚠️ فشل في تحميل بعض الخطوط')
          document.body.classList.add('fonts-failed')
        })

      // مراقبة حالة تحميل الخطوط
      document.fonts.addEventListener('loadingdone', () => {
        console.log('📝 انتهى تحميل الخطوط')
      })

      document.fonts.addEventListener('loadingerror', (event) => {
        console.error('❌ خطأ في تحميل الخط:', event)
      })
    }
  }, [generateGoogleFontUrl])



  useEffect(() => {
    optimizeFonts()
    preloadCriticalFonts()
    setupFontLoadingStrategy()
  }, [optimizeFonts, preloadCriticalFonts, setupFontLoadingStrategy])



  // إضافة CSS للتحسين
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      /* تحسين عرض الخطوط */
      body {
        font-display: swap;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* خط احتياطي أثناء التحميل */
      body:not(.fonts-loaded) {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      }

      /* تحسين الأداء للنصوص العربية */
      [dir="rtl"], .rtl {
        text-align: right;
        direction: rtl;
      }

      /* تحسين عرض النصوص الطويلة */
      .text-content {
        hyphens: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      /* تحسين الخطوط للشاشات عالية الدقة */
      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        body {
          -webkit-font-smoothing: subpixel-antialiased;
        }
      }

      /* تحسين الأداء للطباعة */
      @media print {
        * {
          font-family: 'Times New Roman', serif !important;
          color: black !important;
          background: white !important;
        }
      }

      /* تحسين الخطوط للأجهزة المحمولة */
      @media (max-width: 768px) {
        body {
          font-size: 16px; /* منع التكبير التلقائي في iOS */
        }
      }

      /* تحسين التباين للوضع المظلم */
      @media (prefers-color-scheme: dark) {
        body {
          font-weight: 400; /* خط أثقل قليلاً للوضع المظلم */
        }
      }

      /* تحسين الخطوط للمستخدمين ذوي الاحتياجات الخاصة */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* تحسين قابلية القراءة */
      .high-contrast {
        font-weight: 600;
        letter-spacing: 0.05em;
        line-height: 1.6;
      }

      /* تحسين الخطوط للعناوين */
      h1, h2, h3, h4, h5, h6 {
        font-display: swap;
        font-weight: 600;
        line-height: 1.2;
        letter-spacing: -0.025em;
      }

      /* تحسين الخطوط للنصوص الصغيرة */
      .text-sm, .text-xs {
        letter-spacing: 0.025em;
      }

      /* تحسين الخطوط للأرقام */
      .font-mono, .tabular-nums {
        font-variant-numeric: tabular-nums;
        font-feature-settings: "tnum";
      }

      /* تحسين الخطوط للنصوص العربية */
      .arabic-text {
        font-feature-settings: "liga", "calt", "kern";
        text-rendering: optimizeLegibility;
      }
    `
    
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  // مراقبة أداء الخطوط
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name.includes('font')) {
            console.log(`🔤 تحميل الخط: ${entry.name} في ${entry.duration.toFixed(2)}ms`)
          }
        })
      })

      try {
        observer.observe({ entryTypes: ['resource'] })
      } catch {
        console.warn('Performance Observer غير مدعوم للخطوط')
      }

      return () => observer.disconnect()
    }
  }, [])

  return null // هذا المكون لا يعرض أي شيء
}

// دالة مساعدة لتحسين الخطوط برمجياً
export const fontUtils = {
  // تحميل خط معين
  loadFont: async (family: string, weight: number = 400): Promise<boolean> => {
    if (!('fonts' in document)) return false

    try {
      const fontUrl = `https://fonts.googleapis.com/css2?family=${family}:wght@${weight}&display=swap&subset=arabic`
      const fontFace = new FontFace(family, `url(${fontUrl})`, { weight: weight.toString() })
      
      document.fonts.add(fontFace)
      await fontFace.load()
      
      return true
    } catch (error) {
      console.error('فشل في تحميل الخط:', error)
      return false
    }
  },

  // التحقق من تحميل خط
  isFontLoaded: (family: string): boolean => {
    if (!('fonts' in document)) return false
    return document.fonts.check(`16px ${family}`)
  },

  // تحسين النص للقراءة
  optimizeTextForReadability: (element: HTMLElement): void => {
    element.style.textRendering = 'optimizeLegibility'
    element.style.fontKerning = 'normal'
    element.style.fontVariantLigatures = 'common-ligatures'
    element.style.fontFeatureSettings = '"liga", "calt", "kern"'
  },

  // تطبيق خط احتياطي
  applyFallbackFont: (element: HTMLElement): void => {
    element.style.fontFamily = 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
  }
}

export default FontOptimizer
