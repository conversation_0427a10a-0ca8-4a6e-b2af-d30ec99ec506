'use client'

import React, { useState, useEffect, useRef, useMemo } from 'react'
import { cn } from '@/lib/utils'

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
  onScroll?: (scrollTop: number) => void
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className,
  overscan = 5,
  onScroll
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const { visibleItems, totalHeight, offsetY } = useMemo(() => {
    const containerItemCount = Math.ceil(containerHeight / itemHeight)
    const totalHeight = items.length * itemHeight
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + containerItemCount + overscan,
      items.length - 1
    )

    const visibleStartIndex = Math.max(0, startIndex - overscan)
    const visibleItems = items.slice(visibleStartIndex, endIndex + 1).map((item, index) => ({
      item,
      index: visibleStartIndex + index
    }))

    const offsetY = visibleStartIndex * itemHeight

    return {
      visibleItems,
      totalHeight,
      offsetY
    }
  }, [items, itemHeight, scrollTop, containerHeight, overscan])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    setScrollTop(scrollTop)
    onScroll?.(scrollTop)
  }

  return (
    <div
      ref={scrollElementRef}
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map(({ item, index }) => (
            <div
              key={index}
              style={{ height: itemHeight }}
              className="flex items-center"
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Hook for infinite scrolling with virtual list
export function useInfiniteVirtualList({
  fetchMore,
  hasNextPage,
  threshold = 0.8
}: {
  fetchMore: () => void
  hasNextPage: boolean
  threshold?: number
}) {
  const handleScroll = (scrollTop: number) => {
    const scrollElement = document.querySelector('[data-virtual-list]') as HTMLElement
    if (!scrollElement || !hasNextPage) return

    const { scrollHeight, clientHeight } = scrollElement
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight

    if (scrollPercentage >= threshold) {
      fetchMore()
    }
  }

  return { handleScroll }
}

// Grid virtual list for card layouts
interface VirtualGridProps<T> {
  items: T[]
  itemWidth: number
  itemHeight: number
  containerWidth: number
  containerHeight: number
  gap?: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
}

export function VirtualGrid<T>({
  items,
  itemWidth,
  itemHeight,
  containerWidth,
  containerHeight,
  gap = 16,
  renderItem,
  className
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)

  const { visibleItems, totalHeight, offsetY, columnsPerRow } = useMemo(() => {
    const columnsPerRow = Math.floor((containerWidth + gap) / (itemWidth + gap))
    const rowHeight = itemHeight + gap
    const totalRows = Math.ceil(items.length / columnsPerRow)
    const totalHeight = totalRows * rowHeight

    const startRow = Math.floor(scrollTop / rowHeight)
    const endRow = Math.min(
      startRow + Math.ceil(containerHeight / rowHeight) + 1,
      totalRows - 1
    )

    const startIndex = startRow * columnsPerRow
    const endIndex = Math.min((endRow + 1) * columnsPerRow - 1, items.length - 1)

    const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index
    }))

    const offsetY = startRow * rowHeight

    return {
      visibleItems,
      totalHeight,
      offsetY,
      columnsPerRow
    }
  }, [items, itemWidth, itemHeight, containerWidth, containerHeight, gap, scrollTop])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div 
          style={{ 
            transform: `translateY(${offsetY}px)`,
            display: 'grid',
            gridTemplateColumns: `repeat(${columnsPerRow}, ${itemWidth}px)`,
            gap: `${gap}px`,
            justifyContent: 'start'
          }}
        >
          {visibleItems.map(({ item, index }) => (
            <div key={index} style={{ width: itemWidth, height: itemHeight }}>
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Auto-sizing virtual list that calculates item heights dynamically
interface AutoSizerProps {
  children: (size: { width: number; height: number }) => React.ReactNode
  className?: string
}

export function AutoSizer({ children, className }: AutoSizerProps) {
  const [size, setSize] = useState({ width: 0, height: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current
        setSize({ width: clientWidth, height: clientHeight })
      }
    }

    updateSize()

    const resizeObserver = new ResizeObserver(updateSize)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => resizeObserver.disconnect()
  }, [])

  return (
    <div ref={containerRef} className={cn('w-full h-full', className)}>
      {size.width > 0 && size.height > 0 && children(size)}
    </div>
  )
}

export default VirtualList
