'use client';

import Link from 'next/link';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface LessonBreadcrumbProps {
  yearId: string;
  yearName: string;
  subjectId: string;
  subjectName: string;
}

const LessonBreadcrumb = ({ yearId, yearName, subjectId, subjectName }: LessonBreadcrumbProps) => {
  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList className="space-x-1 rtl:space-x-reverse">
        <BreadcrumbItem>
          <BreadcrumbLink
            asChild
            className="text-foreground hover:text-primary transition-colors"
          >
            <Link href="/levels">الرئيسية</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator className="text-foreground">
          →
        </BreadcrumbSeparator>

        <BreadcrumbItem>
          <BreadcrumbLink
            asChild
            className="text-foreground hover:text-primary transition-colors"
          >
            <Link href={`/year/${yearId}`}>{yearName}</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator className="text-foreground">
          →
        </BreadcrumbSeparator>

        <BreadcrumbItem>
          <BreadcrumbLink
            asChild
            className="text-foreground hover:text-primary transition-colors"
          >
            <Link href={`/subject/${subjectId}`}>{subjectName}</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator className="text-foreground">
          →
        </BreadcrumbSeparator>

        <BreadcrumbItem>
          <BreadcrumbPage className="text-primary">
            تمارين
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default LessonBreadcrumb;
