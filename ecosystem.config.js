// PM2 Configuration for Production (Optional)
// استخدم هذا الملف إذا كان PM2 متاحاً على الخادم

module.exports = {
  apps: [{
    name: 'arab-edu-exercises',
    script: 'server.js',
    instances: 1, // أو 'max' لاستخدام جميع المعالجات
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // إعدادات المراقبة
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    
    // إعدادات إعادة التشغيل
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.next'],
    max_restarts: 10,
    min_uptime: '10s',
    
    // إعدادات الأداء
    node_args: '--max-old-space-size=2048'
  }]
};
