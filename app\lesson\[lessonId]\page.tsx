import { Suspense } from 'react'
import { Metadata } from 'next'
import LessonClient from './lesson-client'
import { getLessonServerSide, getSubjectServerSide } from '@/backend/api/educationAPI'
import { generateLessonMetadata } from '@/lib/seo'
import { decodeIdFromUrl } from '@/utils/id-utils'

type Props = {
  params: { lessonId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const lessonId = decodeIdFromUrl(params.lessonId);
    const lesson = await getLessonServerSide(lessonId)
    if (!lesson) {
      return {
        title: 'الدرس غير موجود - منصة التعليم العربي',
        description: 'لم يتم العثور على الدرس المطلوب',
        robots: { index: false, follow: false }
      }
    }

    // فحص إذا كان الدرس فارغ (بدون تمارين)
    if (!lesson.exercises || lesson.exercises.length === 0) {
      return {
        title: `${lesson.title} - قيد التطوير`,
        description: 'هذا الدرس قيد التطوير وسيتم إضافة المحتوى قريباً',
        robots: { index: false, follow: false }
      }
    }

    const subject = await getSubjectServerSide(lesson.subjectId)
    return generateLessonMetadata(lesson, subject)
  } catch (error) {
    console.error('Error generating lesson metadata:', error)
    return {
      title: 'الدرس - منصة التعليم العربي',
      description: 'استكشف تمارين الدرس التفاعلية',
      robots: { index: false, follow: false }
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function LessonPage({ params }: Props) {
  const lessonId = decodeIdFromUrl(params.lessonId);

  return (
    <Suspense fallback={<PageLoader />}>
      <LessonClient lessonId={lessonId} />
    </Suspense>
  )
}
