/** @type {import('next').NextConfig} */
const nextConfig = {
  // تحسين الصور
  images: {
    domains: ['localhost', 'ckjjqlbzflnxolflixkq.supabase.co', 'talamid.ma', 'd.talamid.ma'],
    unoptimized: false, // تفعيل تحسين الصور
    formats: ['image/webp', 'image/avif'], // تنسيقات محسنة
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Support for Arabic RTL
  i18n: {
    locales: ['ar'],
    defaultLocale: 'ar',
  },

  // تحسين Webpack
  webpack: (config, { dev, isServer }) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      canvas: false,
    };

    // Handle PDF.js worker
    config.module.rules.push({
      test: /pdf\.worker\.(min\.)?js/,
      type: 'asset/resource',
      generator: {
        filename: 'static/worker/[hash][ext][query]',
      },
    });

    // تحسين Bundle في الإنتاج
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
          // React vendor chunk
          react: {
            name: 'react',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 20,
          },
          // UI vendor chunk
          ui: {
            name: 'ui',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react|class-variance-authority|clsx|tailwind-merge)[\\/]/,
            priority: 15,
          },
          // Data vendor chunk
          data: {
            name: 'data',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@supabase|@tanstack|zod)[\\/]/,
            priority: 15,
          },
          // PDF vendor chunk
          pdf: {
            name: 'pdf',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](pdfjs-dist)[\\/]/,
            priority: 15,
          },
          // Common vendor chunk
          vendor: {
            name: 'vendor',
            chunks: 'all',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
          },
        },
      };
    }

    return config;
  },

  // Environment variables
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  },

  // Output configuration
  output: 'standalone',

  // تفعيل Strict Mode للأداء الأفضل
  reactStrictMode: true,

  // تفعيل SWC minification
  swcMinify: true,

  // التجريبية والتحسينات
  experimental: {
    // تحسين الخطوط
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
    // تحسين الذاكرة
    memoryBasedWorkersCount: true,
    // تحسين التحميل
    serverComponentsExternalPackages: ['pdfjs-dist'],
  },

  // ضغط الاستجابات
  compress: true,

  // تحسين الأداء
  poweredByHeader: false,

  // إعادة توجيه للملفات الخاصة
  async rewrites() {
    return [
      {
        source: '/ads.txt',
        destination: '/api/ads-txt',
      },
    ];
  },

  // إعدادات الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      // تحسين cache للملفات الثابتة
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // تحسين cache للصور
      {
        source: '/_next/image(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, stale-while-revalidate=604800',
          },
        ],
      },
      // تحسين cache للخطوط
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // تحسين الأداء للـ API
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, stale-while-revalidate=600',
          },
        ],
      },
      // تحسين الأداء للملفات الديناميكية
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // تحسين الأداء للصفحات
      {
        source: '/((?!api|_next/static|_next/image|favicon.ico).*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
