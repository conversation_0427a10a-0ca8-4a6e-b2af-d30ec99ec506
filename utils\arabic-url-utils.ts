/**
 * Utilities for handling Arabic text in URLs and filenames
 */

/**
 * Decode URL-encoded Arabic characters in filenames
 * @param encodedFilename - The URL-encoded filename
 * @returns Decoded filename with proper Arabic characters
 */
export function decodeArabicFilename(encodedFilename: string): string {
  try {
    // First try standard decodeURIComponent
    return decodeURIComponent(encodedFilename);
  } catch (error) {
    console.warn('Standard decoding failed, trying manual Arabic decode:', error);
    
    try {
      // Manual decode for common Arabic URL encoding patterns
      let decoded = encodedFilename
        // Basic Arabic letters
        .replace(/%D8%A7/g, 'ا')  // ا (alif)
        .replace(/%D8%A8/g, 'ب')  // ب (ba)
        .replace(/%D8%AA/g, 'ت')  // ت (ta)
        .replace(/%D8%AB/g, 'ث')  // ث (tha)
        .replace(/%D8%AC/g, 'ج')  // ج (jim)
        .replace(/%D8%AD/g, 'ح')  // ح (ha)
        .replace(/%D8%AE/g, 'خ')  // خ (kha)
        .replace(/%D8%AF/g, 'د')  // د (dal)
        .replace(/%D8%B0/g, 'ذ')  // ذ (dhal)
        .replace(/%D8%B1/g, 'ر')  // ر (ra)
        .replace(/%D8%B2/g, 'ز')  // ز (zay)
        .replace(/%D8%B3/g, 'س')  // س (sin)
        .replace(/%D8%B4/g, 'ش')  // ش (shin)
        .replace(/%D8%B5/g, 'ص')  // ص (sad)
        .replace(/%D8%B6/g, 'ض')  // ض (dad)
        .replace(/%D8%B7/g, 'ط')  // ط (ta)
        .replace(/%D8%B8/g, 'ظ')  // ظ (za)
        .replace(/%D8%B9/g, 'ع')  // ع (ain)
        .replace(/%D8%BA/g, 'غ')  // غ (ghain)
        .replace(/%D9%81/g, 'ف')  // ف (fa)
        .replace(/%D9%82/g, 'ق')  // ق (qaf)
        .replace(/%D9%83/g, 'ك')  // ك (kaf)
        .replace(/%D9%84/g, 'ل')  // ل (lam)
        .replace(/%D9%85/g, 'م')  // م (mim)
        .replace(/%D9%86/g, 'ن')  // ن (nun)
        .replace(/%D9%87/g, 'ه')  // ه (ha)
        .replace(/%D9%88/g, 'و')  // و (waw)
        .replace(/%D9%8A/g, 'ي')  // ي (ya)
        .replace(/%D9%89/g, 'ى')  // ى (alif maksura)
        .replace(/%D8%A9/g, 'ة')  // ة (ta marbuta)
        .replace(/%D8%A1/g, 'ء')  // ء (hamza)
        .replace(/%D8%A2/g, 'آ')  // آ (alif with madda)
        .replace(/%D8%A3/g, 'أ')  // أ (alif with hamza above)
        .replace(/%D8%A4/g, 'ؤ')  // ؤ (waw with hamza)
        .replace(/%D8%A5/g, 'إ')  // إ (alif with hamza below)
        .replace(/%D8%A6/g, 'ئ')  // ئ (ya with hamza)
        
        // Diacritics (tashkeel)
        .replace(/%D9%8B/g, 'ً')  // ً (fathatan)
        .replace(/%D9%8C/g, 'ٌ')  // ٌ (dammatan)
        .replace(/%D9%8D/g, 'ٍ')  // ٍ (kasratan)
        .replace(/%D9%8E/g, 'َ')  // َ (fatha)
        .replace(/%D9%8F/g, 'ُ')  // ُ (damma)
        .replace(/%D9%90/g, 'ِ')  // ِ (kasra)
        .replace(/%D9%91/g, 'ّ')  // ّ (shadda)
        .replace(/%D9%92/g, 'ْ')  // ْ (sukun)
        
        // Common punctuation and symbols
        .replace(/%20/g, ' ')     // space
        .replace(/%2D/g, '-')     // dash
        .replace(/%2E/g, '.')     // dot
        .replace(/%28/g, '(')     // (
        .replace(/%29/g, ')')     // )
        .replace(/%5F/g, '_')     // underscore
        .replace(/%2B/g, '+')     // plus
        .replace(/%3D/g, '=')     // equals
        .replace(/%26/g, '&')     // ampersand
        .replace(/%23/g, '#')     // hash
        .replace(/%21/g, '!')     // exclamation
        .replace(/%3F/g, '?')     // question mark
        .replace(/%25/g, '%')     // percent
        .replace(/%40/g, '@')     // at symbol
        
        // Arabic numbers
        .replace(/%D9%A0/g, '٠')  // ٠ (Arabic zero)
        .replace(/%D9%A1/g, '١')  // ١ (Arabic one)
        .replace(/%D9%A2/g, '٢')  // ٢ (Arabic two)
        .replace(/%D9%A3/g, '٣')  // ٣ (Arabic three)
        .replace(/%D9%A4/g, '٤')  // ٤ (Arabic four)
        .replace(/%D9%A5/g, '٥')  // ٥ (Arabic five)
        .replace(/%D9%A6/g, '٦')  // ٦ (Arabic six)
        .replace(/%D9%A7/g, '٧')  // ٧ (Arabic seven)
        .replace(/%D9%A8/g, '٨')  // ٨ (Arabic eight)
        .replace(/%D9%A9/g, '٩'); // ٩ (Arabic nine)
      
      return decoded;
    } catch (manualError) {
      console.warn('Manual Arabic decode also failed:', manualError);
      return encodedFilename; // Return original if all attempts fail
    }
  }
}

/**
 * Clean filename to ensure it's valid for file systems
 * @param filename - The filename to clean
 * @returns Cleaned filename safe for file systems
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[<>:"/\\|?*]/g, '_')  // Replace invalid characters with underscore
    .replace(/\s+/g, ' ')           // Normalize multiple spaces to single space
    .replace(/^\.+/, '')            // Remove leading dots
    .replace(/\.+$/, '')            // Remove trailing dots
    .trim();                        // Remove leading/trailing whitespace
}

/**
 * Extract and decode filename from URL
 * @param url - The URL to extract filename from
 * @param defaultName - Default name if extraction fails
 * @returns Decoded and sanitized filename
 */
export function extractFilenameFromUrl(url: string, defaultName: string = 'مستند'): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    let filename = pathname.split('/').pop();

    if (filename && filename.includes('.')) {
      // Decode Arabic characters
      const decodedFilename = decodeArabicFilename(filename);
      // Sanitize for file system
      return sanitizeFilename(decodedFilename);
    }

    // For Google Drive files, create a meaningful filename
    if (url.includes('drive.google.com')) {
      const fileIdMatch = url.match(/\/d\/([^\/]+)/);
      if (fileIdMatch && fileIdMatch[1]) {
        return `${defaultName}_${fileIdMatch[1].substring(0, 8)}.pdf`;
      }
    }

    // Default filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `${defaultName}_${timestamp}.pdf`;
  } catch (error) {
    console.warn('Failed to extract filename from URL:', error);
    // Fallback filename
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `${defaultName}_${timestamp}.pdf`;
  }
}

/**
 * Check if text contains Arabic characters
 * @param text - Text to check
 * @returns True if text contains Arabic characters
 */
export function containsArabic(text: string): boolean {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
}

/**
 * Properly encode URL with Arabic characters for use in viewers
 * @param url - The URL to encode
 * @returns Properly encoded URL
 */
export function encodeArabicUrl(url: string): string {
  try {
    // First decode if already encoded, then re-encode properly
    const decodedUrl = decodeURIComponent(url);
    return encodeURIComponent(decodedUrl);
  } catch (error) {
    // If decoding fails, just encode the original URL
    console.warn('URL encoding warning:', error);
    return encodeURIComponent(url);
  }
}
