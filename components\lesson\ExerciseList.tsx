'use client';

import ExerciseCard from './ExerciseCard';
import { Exercise } from '@/data/types';

interface ExerciseListProps {
  exercises: Exercise[];
}

const ExerciseList = ({ exercises }: ExerciseListProps) => {
  if (!exercises || exercises.length === 0) {
    return <div className="p-4 text-center">لا توجد تمارين متاحة لهذا الدرس</div>;
  }

  return (
    <div className="space-y-8">
      {exercises.map((exercise, index) => (
        <ExerciseCard
          key={exercise.id}
          id={exercise.id}
          question={exercise.question}
          solution={exercise.solution}
          exerciseImageUrl={exercise.exerciseImageUrl}
          solutionImageUrl={exercise.solutionImageUrl}
          index={index}
        />
      ))}
    </div>
  );
};

export default ExerciseList;
