# إضافة حقل الوصف للسنوات الدراسية
# Adding Description Field to Academic Years

## نظرة عامة / Overview

تم إضافة حقل `description` إلى جدول `years` في قاعدة البيانات وتحديث جميع الملفات ذات الصلة لدعم عرض أوصاف مفصلة لكل سنة دراسية.

A `description` field has been added to the `years` table in the database and all related files have been updated to support displaying detailed descriptions for each academic year.

## التغييرات المنفذة / Changes Implemented

### 1. قاعدة البيانات / Database Changes

#### إضافة العمود / Column Addition
```sql
ALTER TABLE public.years 
ADD COLUMN IF NOT EXISTS description text;
```

#### تحديث الأوصاف / Description Updates
تم إضافة أوصاف مخصصة لكل مستوى تعليمي:
- **الابتدائي**: أوصا<PERSON> تركز على بناء الأساسيات والمهارات الأولية
- **الإعدادي**: أوصاف تركز على التطوير والانتقال بين المراحل
- **الجذع المشترك**: أوصاف تركز على التأسيس القوي للتعليم الثانوي
- **الأولى بكالوريا**: أوصاف تركز على بداية التخصص الأكاديمي
- **الثانية بكالوريا**: أوصاف تركز على الإتقان والتحضير للبكالوريا

### 2. أنواع TypeScript / TypeScript Types

#### تحديث نوع Year / Year Type Update
```typescript
// قبل / Before
export interface Year {
  id: string;
  name: string;
  levelId: string;
}

// بعد / After
export interface Year {
  id: string;
  name: string;
  levelId: string;
  description: string;
}
```

#### تحديث أنواع Supabase / Supabase Types Update
```typescript
years: {
  Row: {
    id: string
    level_id: string | null
    name: string
    subjects: string[] | null
    description: string | null  // ✅ جديد
  }
  // ... باقي الأنواع
}
```

### 3. دوال تحميل البيانات / Data Loading Functions

#### تحديث supabaseLoader.ts
```typescript
const mappedYears = (data || []).map(year => ({
  id: year.id,
  name: year.name,
  levelId: year.level_id || '',
  description: year.description || 'استكشف المواد الدراسية لهذه السنة' // ✅ جديد
}));
```

### 4. واجهة المستخدم / User Interface

#### تحديث year-client.tsx
```tsx
<p className="text-muted-foreground mx-auto max-w-2xl arabic-text">
  {year.description}  {/* ✅ يعرض الوصف من قاعدة البيانات */}
</p>
```

## أمثلة على الأوصاف / Description Examples

### السنوات الابتدائية / Primary Years
- **الأولى ابتدائي**: "السنة الأولى من التعليم الابتدائي - بداية الرحلة التعليمية وتعلم الأساسيات"
- **السادسة ابتدائي**: "السنة السادسة من التعليم الابتدائي - إتمام المرحلة الابتدائية والاستعداد للإعدادي"

### السنوات الثانوية / Secondary Years
- **العلوم الرياضية**: "السنة الثانية من التعليم الثانوي - تخصص في الرياضيات والعلوم الدقيقة"
- **الثانية باك فيزياء**: "السنة الأخيرة من التعليم الثانوي - إتقان الفيزياء والكيمياء والتحضير للبكالوريا"

## الملفات المحدثة / Updated Files

1. `scripts/add-description-to-years.sql` - سكريبت إضافة العمود والأوصاف
2. `data/types.ts` - تحديث نوع Year
3. `integrations/supabase/types.ts` - تحديث أنواع Supabase
4. `backend/utils/supabaseLoader.ts` - تحديث دوال تحميل البيانات
5. `app/year/[yearId]/year-client.tsx` - عرض الوصف في الواجهة

## الفوائد / Benefits

✅ **تحسين تجربة المستخدم**: أوصاف واضحة ومفيدة لكل سنة دراسية
✅ **محتوى عربي أصيل**: أوصاف باللغة العربية تناسب السياق التعليمي المغربي
✅ **مرونة في التحديث**: يمكن تحديث الأوصاف بسهولة من قاعدة البيانات
✅ **تطابق مع النظام التعليمي**: أوصاف تعكس خصائص كل مرحلة تعليمية

## كيفية إضافة أوصاف جديدة / How to Add New Descriptions

```sql
UPDATE public.years 
SET description = 'الوصف الجديد هنا'
WHERE id = 'year_id_here';
```

## الاختبار / Testing

تم اختبار التحديثات والتأكد من:
- ✅ إضافة العمود بنجاح في قاعدة البيانات
- ✅ تحديث جميع الأنواع TypeScript
- ✅ عمل دوال تحميل البيانات بشكل صحيح
- ✅ عرض الأوصاف في واجهة المستخدم
- ✅ عدم وجود أخطاء TypeScript
