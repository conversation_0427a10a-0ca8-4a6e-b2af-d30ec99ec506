-- سكريبت شامل لإصلاح ترتيب المستويات ومسح التخزين المؤقت
-- يجب تنفيذ هذا السكريبت في Supabase ثم مسح التخزين المحلي في المتصفح

-- 1. إضافة عمود الترتيب إذا لم يكن موجوداً
ALTER TABLE levels ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;

-- 2. تحديث ترتيب العرض للمستويات بالترتيب التعليمي الصحيح
UPDATE levels SET display_order = 1 WHERE id = 'primary';
UPDATE levels SET display_order = 2 WHERE id = 'middle';
UPDATE levels SET display_order = 3 WHERE id = 'trunk_common';
UPDATE levels SET display_order = 4 WHERE id = 'first_bac';
UPDATE levels SET display_order = 5 WHERE id = 'second_bac';

-- 3. التحقق من النتائج
SELECT 'ترتيب المستويات الجديد (الصحيح):' as info;
SELECT 
    display_order as "الترتيب",
    name as "اسم المستوى",
    id as "معرف المستوى"
FROM levels
WHERE id IN ('primary', 'middle', 'trunk_common', 'first_bac', 'second_bac')
ORDER BY display_order;

-- 4. عرض جميع المستويات مرتبة
SELECT 'جميع المستويات مرتبة:' as verification;
SELECT 
    display_order,
    id,
    name,
    description
FROM levels
ORDER BY display_order;

-- 5. إحصائيات الترتيب
SELECT 
    'إحصائيات الترتيب' as stats,
    COUNT(*) as total_levels,
    MIN(display_order) as min_order,
    MAX(display_order) as max_order
FROM levels
WHERE display_order > 0;

-- ملاحظة مهمة:
-- بعد تنفيذ هذا السكريبت، يجب مسح التخزين المحلي في المتصفح
-- افتح Developer Tools (F12) -> Application -> Local Storage -> امسح جميع البيانات
-- أو استخدم الكود التالي في Console المتصفح:
-- localStorage.clear(); location.reload();
