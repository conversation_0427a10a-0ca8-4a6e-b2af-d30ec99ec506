import { NextRequest, NextResponse } from 'next/server';
import { getSingleYearServerSide } from '@/backend/utils/dataLoader';
import { decodeIdFromUrl } from '@/utils/id-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { yearId: string } }
) {
  try {
    const { yearId: encodedYearId } = params;

    if (!encodedYearId) {
      return NextResponse.json(
        { error: 'yearId is required' },
        { status: 400 }
      );
    }

    // فك ترميز المعرف من الرابط
    const yearId = decodeIdFromUrl(encodedYearId);

    console.log(`API: جاري جلب السنة ${yearId}...`);

    // جلب السنة مباشرة من Supabase
    const year = await getSingleYearServerSide(yearId);
    
    if (!year) {
      return NextResponse.json(
        { error: 'Year not found' },
        { status: 404 }
      );
    }

    console.log(`API: تم جلب السنة "${year.name}" بنجاح`);
    
    return NextResponse.json({
      success: true,
      year: {
        id: year.id,
        name: year.name,
        levelId: year.levelId,
        description: year.description
      }
    });
  } catch (error) {
    console.error('Error fetching year:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
