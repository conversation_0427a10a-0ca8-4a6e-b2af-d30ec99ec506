import { NextRequest, NextResponse } from 'next/server';
import { getSingleLevelServerSide } from '@/backend/utils/dataLoader';

export async function GET(
  request: NextRequest,
  { params }: { params: { levelId: string } }
) {
  try {
    const { levelId } = params;

    if (!levelId) {
      return NextResponse.json(
        { error: 'levelId is required' },
        { status: 400 }
      );
    }

    console.log(`API: جاري جلب المستوى ${levelId}...`);

    // جلب المستوى مباشرة من Supabase
    const level = await getSingleLevelServerSide(levelId);
    
    if (!level) {
      return NextResponse.json(
        { error: 'Level not found' },
        { status: 404 }
      );
    }

    console.log(`API: تم جلب المستوى "${level.name}" بنجاح`);
    
    return NextResponse.json({
      success: true,
      level: {
        id: level.id,
        name: level.name,
        description: level.description
      }
    });
  } catch (error) {
    console.error('Error fetching level:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
