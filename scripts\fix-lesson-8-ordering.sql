-- سكريبت إصلاح ترتيب الدرس 8 في قاعدة بيانات Supabase
-- Fix Lesson 8 Ordering Script for Supabase Database
-- 
-- المشكلة: كان لدينا 6 دروس بنفس display_order = 8
-- الحل: تغيير نوع العمود إلى numeric وإعطاء ترقيم فرعي

-- 1. تغيير نوع عمود display_order من integer إلى numeric لدعم الترقيم العشري
ALTER TABLE lessons ALTER COLUMN display_order TYPE NUMERIC(5,2);

-- 2. تحديث ترتيب الدروس للدرس 8 باستخدام الترقيم الفرعي

-- 8.1: تقديم ومقارنة الأعداد العشرية النسبية
UPDATE lessons 
SET display_order = 8.1 
WHERE id IN (
  '1ac_math_08_tqdim-omqarna-alaadad-alashria-alnsbia_doros',
  '1ac_math_08_tqdim-omqarna-alaadad-alashria-alnsbia_tamarin'
);

-- 8.2: جمع وطرح الأعداد العشرية النسبية
UPDATE lessons 
SET display_order = 8.2 
WHERE id IN (
  '1ac_math_08_jma-otrh-alaadad-alashria-alnsbia_doros',
  '1ac_math_08_jma-otrh-alaadad-alashria-alnsbia_tamarin'
);

-- 8.3: ضرب وقسمة الأعداد العشرية النسبية
UPDATE lessons 
SET display_order = 8.3 
WHERE id IN (
  '1ac_math_08_dhrb-oqsma-alaadad-alashria-alnsbia_doros',
  '1ac_math_08_dhrb-oqsma-alaadad-alashria-alnsbia_tamarin'
);

-- 3. التحقق من النتائج
SELECT 'ترتيب الدروس الجديد للدرس 8:' as info;
SELECT 
  id, 
  title, 
  content_type, 
  display_order 
FROM lessons 
WHERE display_order >= 8 AND display_order < 9 
ORDER BY display_order, content_type;

-- 4. التحقق من جميع الدروس للتأكد من عدم وجود تضارب في الترتيب
SELECT 'جميع الدروس مرتبة حسب display_order:' as info;
SELECT 
  display_order, 
  COUNT(*) as lesson_count,
  STRING_AGG(DISTINCT content_type, ', ') as content_types
FROM lessons 
GROUP BY display_order 
ORDER BY display_order;
