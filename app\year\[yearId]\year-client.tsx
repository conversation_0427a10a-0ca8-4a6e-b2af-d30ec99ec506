'use client'

import Link from 'next/link';
import { ArrowLeft, BookOpen } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useYearWithSubjects } from '@/hooks/use-education-data';

interface YearClientProps {
  yearId: string;
}

export default function YearClient({ yearId }: YearClientProps) {
  // استخدام React Query hook للحصول على البيانات
  const { year, subjects, isLoading, isError } = useYearWithSubjects(yearId);

  const loading = isLoading;
  const error = isError;

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg arabic-text">جاري تحميل البيانات...</p>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  if (error || !year) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <h1 className="text-2xl font-bold text-destructive mb-4 arabic-heading">خطأ</h1>
            <p className="text-lg mb-4 arabic-text">{'لم يتم العثور على السنة الدراسية'}</p>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors rtl-button"
              dir="rtl"
            >
              العودة إلى المستويات
              <ArrowLeft className="h-4 w-4 ml-2 " />
            </Link>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4" style={{ textAlign: 'center' }}>{year.name}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl" dir="rtl">
            {year.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8" dir="rtl">
          <nav className="rtl-breadcrumb">
            <Link href="/levels" className="hover:text-primary transition-colors arabic-text">
              المستويات
            </Link>
            <span className="rtl-breadcrumb-separator">/</span>
            <span className="text-foreground arabic-text">{year.name}</span>
          </nav>
        </div>

        {/* Subjects Grid */}
        {subjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subjects.map((subject) => (
              <Link
                key={subject.id}
                href={`/subject/${subject.id}`}
                className="level-card transition-all duration-300 hover:shadow-md hover:scale-[1.02]"
                dir="rtl"
              >
                <div className="flex items-center gap-3 rtl-flex-row-reverse">
                  <span className="text-xl font-bold arabic-heading-inline">{subject.name}</span>
                  {subject.icon ? (
                    <span className="text-3xl inline-block align-middle">{subject.icon}</span>
                  ) : (
                    <BookOpen className="h-8 w-8 text-primary align-middle" />
                  )}
                </div>
                <div className="bg-muted rounded-full p-2 transition-all duration-300 hover:bg-primary/20">
                  <ArrowLeft className="h-5 w-5 text-primary transition-transform hover:translate-x-1 " />
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12" dir="rtl">
            <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2 arabic-heading">لا توجد مواد دراسية</h3>
            <p className="text-muted-foreground mb-6 arabic-text">
              لم يتم العثور على مواد دراسية لهذه السنة حالياً
            </p>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors rtl-button"
              dir="rtl"
            >
              العودة إلى المستويات
              <ArrowLeft className="h-4 w-4 ml-2 " />
            </Link>
          </div>
        )}
      </div>

      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
}
