'use client'

import Link from 'next/link'
import { ArrowLeft, Construction, Clock } from 'lucide-react'

interface EmptyContentMessageProps {
  type: 'lesson' | 'homework' | 'summary' | 'exam'
  title: string
  subjectId?: string
  returnTo?: string
}

export default function EmptyContentMessage({ 
  type, 
  title, 
  subjectId, 
  returnTo 
}: EmptyContentMessageProps) {
  const getTypeText = () => {
    switch (type) {
      case 'lesson': return 'الدرس'
      case 'homework': return 'الفرض المنزلي'
      case 'summary': return 'الملخص'
      case 'exam': return 'الامتحان'
      default: return 'المحتوى'
    }
  }

  const getDescription = () => {
    switch (type) {
      case 'lesson': return 'سيتم إضافة التمارين والأنشطة التفاعلية قريباً'
      case 'homework': return 'سيتم إضافة الفروض والتمارين المنزلية قريباً'
      case 'summary': return 'سيتم إضافة الملخص والنقاط المهمة قريباً'
      case 'exam': return 'سيتم إضافة أسئلة الامتحان والتقييم قريباً'
      default: return 'سيتم إضافة المحتوى قريباً'
    }
  }

  return (
    <div className="text-center py-16 px-4">
      <div className="max-w-md mx-auto">
        <div className="relative mb-6">
          <Construction className="h-20 w-20 text-orange-400 mx-auto mb-2" />
          <Clock className="h-8 w-8 text-orange-300 absolute -top-1 -right-1 animate-pulse" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-800 mb-3">
          {getTypeText()} قيد التطوير
        </h2>
        
        <h3 className="text-lg font-semibold text-gray-600 mb-4">
          {title}
        </h3>
        
        <p className="text-gray-500 mb-6 leading-relaxed">
          {getDescription()}
        </p>
        
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <p className="text-orange-700 text-sm">
            <strong>ملاحظة:</strong> نحن نعمل بجد لإضافة محتوى عالي الجودة. 
            تابعنا للحصول على التحديثات الجديدة!
          </p>
        </div>
        
        <div className="space-y-3">
          {subjectId && (
            <Link
              href={returnTo || `/subject/${subjectId}`}
              className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة إلى المادة
            </Link>
          )}
          
          <div>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 text-primary hover:text-primary/80 transition-colors"
            >
              تصفح المراحل الدراسية
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
