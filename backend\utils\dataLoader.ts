import { Level, Year, Subject, Lesson, Exercise } from '@/data/types';
import {
  fetchLevelsFromSupabase,
  fetchLevelFromSupabase,
  fetchYearsFromSupabase,
  fetchYearFromSupabase,
  fetchSubjectsFromSupabase,
  fetchSubjectFromSupabase,
  fetchLessonsFromSupabase,
  fetchLessonFromSupabase,
  fetchYearsForLevelFromSupabase,
  fetchSubjectsForYearFromSupabase,
  fetchLessonsForSubjectFromSupabase
} from './supabaseLoader';
import {
  getLevelsFromStorage,
  getYearsFromStorage,
  getSubjectsFromStorage,
  getLessonsFromStorage,
  getYearsByLevelFromStorage,
  getSubjectsByYearFromStorage,
  getLessonsBySubjectFromStorage,
  saveLevels,
  saveYears,
  saveSubjects,
  saveLessons,
  saveYearsByLevel,
  saveSubjectsByYear,
  saveLessonsBySubject,
  clearAllData,
  hasLevels,
  hasYears,
  hasSubjects,
  hasLessons,
  updateLevelsIfChanged,
  updateYearsIfChanged,
  updateSubjectsIfChanged,
  updateLessonsIfChanged
} from './enhancedStorageManager';

// تعريف هياكل البيانات الفارغة الاحتياطية
const emptyLevels: Level[] = [];
const emptyYears: Year[] = [];
const emptySubjects: Subject[] = [];
const emptyLessons: Lesson[] = [];

// حدد مصدر البيانات (دائماً Supabase)
let USE_SUPABASE = true;

// تخزين مؤقت في الذاكرة للبيانات المستخدمة بشكل متكرر
const memoryCache: {
  levels?: Level[];
  years?: Year[];
  subjects?: Subject[];
  lessons?: Lesson[];
  yearsByLevel: Record<string, Year[]>;
  subjectsByYear: Record<string, Subject[]>;
  lessonsBySubject: Record<string, Lesson[]>;
} = {
  yearsByLevel: {},
  subjectsByYear: {},
  lessonsBySubject: {}
};

// دالة لمسح التخزين المؤقت للدروس
export function clearLessonsCache(): void {
  console.log('مسح التخزين المؤقت للدروس...');
  memoryCache.lessons = undefined;
  memoryCache.lessonsBySubject = {};
  console.log('تم مسح التخزين المؤقت للدروس بنجاح');
}

// وظائف جلب البيانات
export async function getLevels(): Promise<Level[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.levels && memoryCache.levels.length > 0) {
      console.log(`تم استرجاع ${memoryCache.levels.length} مستويات من الذاكرة المؤقتة`);
      return memoryCache.levels;
    }

    // محاولة استرجاع البيانات من التخزين المحسن
    const cachedData = getLevelsFromStorage();
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} مستويات من التخزين المحسن`);
      memoryCache.levels = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة، جلبها من Supabase
    console.log('جاري جلب المستويات من Supabase...');
    const data = await fetchLevelsFromSupabase();
    console.log(`تم جلب ${data.length} مستويات من Supabase`);

    // حفظ البيانات باستخدام النظام المحسن
    const saved = updateLevelsIfChanged(data);
    if (saved) {
      console.log('تم تحديث المستويات في التخزين المحسن');
    }

    memoryCache.levels = data;
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المستويات:`, error);
    return emptyLevels;
  }
}

export async function getYears(): Promise<Year[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.years && memoryCache.years.length > 0) {
      console.log(`تم استرجاع ${memoryCache.years.length} سنوات دراسية من الذاكرة المؤقتة`);
      return memoryCache.years;
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getYearsFromStorage();
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} سنوات دراسية من التخزين المحلي`);
      memoryCache.years = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log('جاري جلب السنوات الدراسية من Supabase...');
    const data = await fetchYearsFromSupabase();
    console.log(`تم جلب ${data.length} سنوات دراسية من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveYears(data);
    memoryCache.years = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب السنوات الدراسية:`, error);
    return emptyYears;
  }
}

export async function getSubjects(): Promise<Subject[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.subjects && memoryCache.subjects.length > 0) {
      console.log(`تم استرجاع ${memoryCache.subjects.length} مواد دراسية من الذاكرة المؤقتة`);
      return memoryCache.subjects;
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getSubjectsFromStorage();
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} مواد دراسية من التخزين المحلي`);
      memoryCache.subjects = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log('جاري جلب المواد الدراسية من Supabase...');
    const data = await fetchSubjectsFromSupabase();
    console.log(`تم جلب ${data.length} مواد دراسية من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveSubjects(data);
    memoryCache.subjects = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المواد الدراسية:`, error);
    return emptySubjects;
  }
}

export async function getLessons(): Promise<Lesson[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.lessons && memoryCache.lessons.length > 0) {
      console.log(`تم استرجاع ${memoryCache.lessons.length} دروس من الذاكرة المؤقتة`);
      return memoryCache.lessons;
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getLessonsFromStorage();
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} دروس من التخزين المحلي`);
      memoryCache.lessons = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log('جاري جلب الدروس من Supabase...');
    const data = await fetchLessonsFromSupabase();
    console.log(`تم جلب ${data.length} دروس من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveLessons(data);
    memoryCache.lessons = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب الدروس:`, error);
    return emptyLessons;
  }
}

// وظائف جلب البيانات المرتبطة
export async function getYearsForLevel(levelId: string): Promise<Year[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.yearsByLevel[levelId]) {
      console.log(`تم استرجاع سنوات المستوى ${levelId} من الذاكرة المؤقتة`);
      return memoryCache.yearsByLevel[levelId];
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getYearsByLevelFromStorage(levelId);
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} سنوات للمستوى ${levelId} من التخزين المحلي`);
      memoryCache.yearsByLevel[levelId] = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log(`جاري جلب سنوات المستوى ${levelId} من Supabase...`);
    const data = await fetchYearsForLevelFromSupabase(levelId);
    console.log(`تم جلب ${data.length} سنوات للمستوى ${levelId} من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveYearsByLevel(levelId, data);
    memoryCache.yearsByLevel[levelId] = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب سنوات المستوى ${levelId}:`, error);
    return emptyYears;
  }
}

export async function getSubjectsForYear(yearId: string): Promise<Subject[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.subjectsByYear[yearId]) {
      console.log(`تم استرجاع مواد السنة ${yearId} من الذاكرة المؤقتة`);
      return memoryCache.subjectsByYear[yearId];
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getSubjectsByYearFromStorage(yearId);
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} مواد للسنة ${yearId} من التخزين المحلي`);
      memoryCache.subjectsByYear[yearId] = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log(`جاري جلب مواد السنة ${yearId} من Supabase...`);
    const data = await fetchSubjectsForYearFromSupabase(yearId);
    console.log(`تم جلب ${data.length} مواد للسنة ${yearId} من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveSubjectsByYear(yearId, data);
    memoryCache.subjectsByYear[yearId] = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب مواد السنة ${yearId}:`, error);
    return emptySubjects;
  }
}

export async function getLessonsForSubject(subjectId: string): Promise<Lesson[]> {
  try {
    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (memoryCache.lessonsBySubject[subjectId]) {
      console.log(`تم استرجاع دروس المادة ${subjectId} من الذاكرة المؤقتة`);
      return memoryCache.lessonsBySubject[subjectId];
    }

    // محاولة استرجاع البيانات من التخزين المحلي
    const cachedData = getLessonsBySubjectFromStorage(subjectId);
    if (cachedData && cachedData.length > 0) {
      console.log(`تم استرجاع ${cachedData.length} دروس للمادة ${subjectId} من التخزين المحلي`);
      memoryCache.lessonsBySubject[subjectId] = cachedData; // تخزين في الذاكرة المؤقتة
      return cachedData;
    }

    // إذا لم تكن البيانات موجودة في التخزين المحلي، جلبها من Supabase
    console.log(`جاري جلب دروس المادة ${subjectId} من Supabase...`);
    const data = await fetchLessonsForSubjectFromSupabase(subjectId);
    console.log(`تم جلب ${data.length} دروس للمادة ${subjectId} من Supabase`);

    // حفظ البيانات في التخزين المحلي والذاكرة المؤقتة
    saveLessonsBySubject(subjectId, data);
    memoryCache.lessonsBySubject[subjectId] = data;

    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب دروس المادة ${subjectId}:`, error);
    return emptyLessons;
  }
}

// دالة محسنة لجلب درس واحد مباشرة من Supabase
export async function getSingleLesson(lessonId: string): Promise<Lesson | null> {
  try {
    console.log(`جاري جلب الدرس ${lessonId} مباشرة...`);

    // جلب الدرس مباشرة من Supabase دون تحميل جميع الدروس
    const lesson = await fetchLessonFromSupabase(lessonId);

    if (lesson) {
      console.log(`تم جلب الدرس "${lesson.title}" بنجاح`);
    } else {
      console.log(`لم يتم العثور على الدرس ${lessonId}`);
    }

    return lesson;
  } catch (error) {
    console.error(`خطأ أثناء جلب الدرس ${lessonId}:`, error);
    return null;
  }
}

// دالة محسنة لجلب مادة واحدة مباشرة من Supabase
export async function getSingleSubject(subjectId: string): Promise<Subject | null> {
  try {
    console.log(`جاري جلب المادة ${subjectId} مباشرة...`);

    // جلب المادة مباشرة من Supabase دون تحميل جميع المواد
    const subject = await fetchSubjectFromSupabase(subjectId);

    if (subject) {
      console.log(`تم جلب المادة "${subject.name}" بنجاح`);
    } else {
      console.log(`لم يتم العثور على المادة ${subjectId}`);
    }

    return subject;
  } catch (error) {
    console.error(`خطأ أثناء جلب المادة ${subjectId}:`, error);
    return null;
  }
}

// دالة محسنة لجلب سنة واحدة مباشرة من Supabase
export async function getSingleYear(yearId: string): Promise<Year | null> {
  try {
    console.log(`جاري جلب السنة ${yearId} مباشرة...`);

    // جلب السنة مباشرة من Supabase دون تحميل جميع السنوات
    const year = await fetchYearFromSupabase(yearId);

    if (year) {
      console.log(`تم جلب السنة "${year.name}" بنجاح`);
    } else {
      console.log(`لم يتم العثور على السنة ${yearId}`);
    }

    return year;
  } catch (error) {
    console.error(`خطأ أثناء جلب السنة ${yearId}:`, error);
    return null;
  }
}

// دالة محسنة لجلب مستوى واحد مباشرة من Supabase
export async function getSingleLevel(levelId: string): Promise<Level | null> {
  try {
    console.log(`جاري جلب المستوى ${levelId} مباشرة...`);

    // جلب المستوى مباشرة من Supabase دون تحميل جميع المستويات
    const level = await fetchLevelFromSupabase(levelId);

    if (level) {
      console.log(`تم جلب المستوى "${level.name}" بنجاح`);
    } else {
      console.log(`لم يتم العثور على المستوى ${levelId}`);
    }

    return level;
  } catch (error) {
    console.error(`خطأ أثناء جلب المستوى ${levelId}:`, error);
    return null;
  }
}

// تصدير مصفوفات البيانات الفارغة للمكونات التي تتوقع وصولاً متزامناً
export const levels: Level[] = emptyLevels;
export const years: Year[] = emptyYears;
export const subjects: Subject[] = emptySubjects;
export const lessons: Lesson[] = emptyLessons;

// نحتفظ بهذه الدوال للتوافقية الخلفية ولكنها لم تعد تفعل شيئاً
export const setDataSource = (url: string | null) => {
  // لا شيء، نستخدم دائماً Supabase
};

export const toggleDataSource = (useSupabase: boolean) => {
  // لا شيء، نستخدم دائماً Supabase
};

export const getDataSource = (): string => {
  return 'Supabase';
};

// دالة لمسح جميع البيانات المؤقتة وإجبار جلب البيانات الجديدة
export const clearAllCacheAndRefresh = (): void => {
  console.log('🧹 مسح جميع البيانات المؤقتة...');

  // مسح الذاكرة المؤقتة
  memoryCache.levels = undefined;
  memoryCache.years = undefined;
  memoryCache.subjects = undefined;
  memoryCache.lessons = undefined;
  memoryCache.yearsByLevel = {};
  memoryCache.subjectsByYear = {};
  memoryCache.lessonsBySubject = {};

  // مسح التخزين المحلي
  clearAllData();

  console.log('✨ تم مسح جميع البيانات المؤقتة بنجاح!');
  console.log('🔄 البيانات الجديدة ستُجلب من Supabase في الطلب التالي');
};

// دوال خاصة للاستخدام في بيئة الخادم (Server-Side) - تتجنب localStorage
export async function getYearsServerSide(): Promise<Year[]> {
  try {
    // في بيئة الخادم، نجلب البيانات مباشرة من Supabase
    console.log('جاري جلب السنوات الدراسية من Supabase (Server-Side)...');
    const data = await fetchYearsFromSupabase();
    console.log(`تم جلب ${data.length} سنوات دراسية من Supabase (Server-Side)`);
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب السنوات الدراسية (Server-Side):`, error);
    return [];
  }
}

export async function getSubjectsServerSide(): Promise<Subject[]> {
  try {
    console.log('جاري جلب المواد الدراسية من Supabase (Server-Side)...');
    const data = await fetchSubjectsFromSupabase();
    console.log(`تم جلب ${data.length} مواد دراسية من Supabase (Server-Side)`);
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المواد الدراسية (Server-Side):`, error);
    return [];
  }
}

export async function getLevelsServerSide(): Promise<Level[]> {
  try {
    console.log('جاري جلب المستويات من Supabase (Server-Side)...');
    const data = await fetchLevelsFromSupabase();
    console.log(`تم جلب ${data.length} مستويات من Supabase (Server-Side)`);
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المستويات (Server-Side):`, error);
    return [];
  }
}

export async function getLessonsServerSide(): Promise<Lesson[]> {
  try {
    console.log('جاري جلب الدروس من Supabase (Server-Side)...');
    const data = await fetchLessonsFromSupabase();
    console.log(`تم جلب ${data.length} دروس من Supabase (Server-Side)`);
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب الدروس (Server-Side):`, error);
    return [];
  }
}

export async function getSingleLessonServerSide(lessonId: string): Promise<Lesson | null> {
  try {
    console.log(`جاري جلب الدرس ${lessonId} من Supabase (Server-Side)...`);
    const data = await fetchLessonFromSupabase(lessonId);
    if (data) {
      console.log(`تم جلب الدرس "${data.title}" من Supabase (Server-Side)`);
    } else {
      console.log(`لم يتم العثور على الدرس ${lessonId} (Server-Side)`);
    }
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب الدرس ${lessonId} (Server-Side):`, error);
    return null;
  }
}

export async function getSingleSubjectServerSide(subjectId: string): Promise<Subject | null> {
  try {
    console.log(`جاري جلب المادة ${subjectId} من Supabase (Server-Side)...`);
    const data = await fetchSubjectFromSupabase(subjectId);
    if (data) {
      console.log(`تم جلب المادة "${data.name}" من Supabase (Server-Side)`);
    } else {
      console.log(`لم يتم العثور على المادة ${subjectId} (Server-Side)`);
    }
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المادة ${subjectId} (Server-Side):`, error);
    return null;
  }
}

export async function getSingleYearServerSide(yearId: string): Promise<Year | null> {
  try {
    console.log(`جاري جلب السنة ${yearId} من Supabase (Server-Side)...`);
    const data = await fetchYearFromSupabase(yearId);
    if (data) {
      console.log(`تم جلب السنة "${data.name}" من Supabase (Server-Side)`);
    } else {
      console.log(`لم يتم العثور على السنة ${yearId} (Server-Side)`);
    }
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب السنة ${yearId} (Server-Side):`, error);
    return null;
  }
}

export async function getSingleLevelServerSide(levelId: string): Promise<Level | null> {
  try {
    console.log(`جاري جلب المستوى ${levelId} من Supabase (Server-Side)...`);
    const data = await fetchLevelFromSupabase(levelId);
    if (data) {
      console.log(`تم جلب المستوى "${data.name}" من Supabase (Server-Side)`);
    } else {
      console.log(`لم يتم العثور على المستوى ${levelId} (Server-Side)`);
    }
    return data;
  } catch (error) {
    console.error(`خطأ أثناء جلب المستوى ${levelId} (Server-Side):`, error);
    return null;
  }
}
