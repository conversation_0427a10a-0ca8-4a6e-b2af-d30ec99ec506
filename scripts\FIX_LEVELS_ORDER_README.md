# إصلاح ترتيب المستويات التعليمية

## المشكلة

الترتيب الحالي في الصفحة الرئيسية غير صحيح. يظهر:
- جذع مشترك أولاً
- ثم الأولى باك
- ثم الثانية باك
- ثم الابتدائي
- ثم الإعدادي

## الترتيب الصحيح المطلوب

يجب أن يكون الترتيب التعليمي الطبيعي:
1. **الابتدائي** (6 سنوات)
2. **الإعدادي** (3 سنوات)
3. **جذع مشترك** (3 تخصصات)
4. **الأولى باك** (6 تخصصات)
5. **الثانية باك** (11 تخصص)

## الحل

### 1. تحديث قاعدة البيانات

استخدم السكريبت `fix-levels-order.sql` لتصحيح الترتيب في Supabase:

```sql
-- إضافة عمود الترتيب إذا لم يكن موجوداً
ALTER TABLE levels ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;

-- تحديث الترتيب الصحيح
UPDATE levels SET display_order = 1 WHERE id = 'primary';
UPDATE levels SET display_order = 2 WHERE id = 'middle';
UPDATE levels SET display_order = 3 WHERE id = 'trunk_common';
UPDATE levels SET display_order = 4 WHERE id = 'first_bac';
UPDATE levels SET display_order = 5 WHERE id = 'second_bac';
```

### 2. تحديث الكود

تم تحديث دالة `fetchLevelsFromSupabase()` في ملف `src/backend/utils/supabaseLoader.ts` لتستخدم الترتيب:

```typescript
const { data, error } = await supabase
  .from('levels')
  .select('*')
  .order('display_order', { ascending: true });
```

## كيفية التطبيق

### الطريقة 1: استخدام Supabase SQL Editor (الموصى بها)

1. افتح Supabase Dashboard
2. انتقل إلى SQL Editor
3. انسخ محتوى ملف `fix-levels-order.sql`
4. الصق المحتوى في المحرر
5. اضغط على "Run" لتنفيذ السكريبت

### الطريقة 2: التحقق من النتائج

بعد تنفيذ السكريبت، ستحصل على:

```
ترتيب المستويات الجديد (الصحيح):
الترتيب | اسم المستوى | معرف المستوى
---------|-------------|---------------
1        | الابتدائي   | primary
2        | الإعدادي    | middle
3        | جذع مشترك   | trunk_common
4        | الأولى باك  | first_bac
5        | الثانية باك | second_bac
```

## التحقق من النتائج

بعد تطبيق الإصلاح:

1. **في قاعدة البيانات**: ستجد عمود `display_order` مع القيم الصحيحة
2. **في التطبيق**: ستظهر المستويات بالترتيب التعليمي الطبيعي
3. **في الصفحة الرئيسية**: الابتدائي → الإعدادي → جذع مشترك → الأولى باك → الثانية باك

## الملفات المحدثة

1. **`scripts/fix-levels-order.sql`** - السكريبت الرئيسي لإصلاح الترتيب
2. **`src/backend/utils/supabaseLoader.ts`** - تحديث دالة جلب المستويات لتستخدم الترتيب
3. **`scripts/FIX_LEVELS_ORDER_README.md`** - هذا الدليل

## ملاحظات مهمة

✅ **لا يؤثر على البيانات**: هذا التحديث يؤثر فقط على ترتيب العرض

✅ **آمن**: لا يحذف أو يغير أي بيانات موجودة

✅ **فوري**: التأثير يظهر فوراً بعد تنفيذ السكريبت

✅ **متوافق**: يعمل مع جميع التحديثات السابقة

## النتيجة النهائية

بعد تطبيق هذا الإصلاح، ستظهر المستويات في الصفحة الرئيسية بالترتيب التعليمي الصحيح:

### الترتيب الجديد:
1. 🎒 **الابتدائي** - 6 سنوات دراسية
2. 📚 **الإعدادي** - 3 سنوات دراسية  
3. 🌟 **جذع مشترك** - 3 تخصصات
4. 🎓 **الأولى باك** - 6 تخصصات
5. 🏆 **الثانية باك** - 11 تخصص

هذا الترتيب يعكس المسار التعليمي الطبيعي للطالب المغربي من المرحلة الابتدائية حتى الثانوية.

## استكشاف الأخطاء

إذا لم يظهر الترتيب الصحيح بعد تطبيق السكريبت:

1. **تحقق من قاعدة البيانات**: تأكد من وجود عمود `display_order` وقيمه الصحيحة
2. **امسح الذاكرة المؤقتة**: أعد تحميل الصفحة أو امسح ذاكرة المتصفح
3. **تحقق من الكونسول**: ابحث عن أي رسائل خطأ في كونسول المطور
4. **أعد تشغيل التطبيق**: في بيئة التطوير، أعد تشغيل الخادم

## الدعم

إذا واجهت أي مشاكل، تحقق من:
- رسائل الكونسول في المتصفح
- سجلات Supabase
- التأكد من تنفيذ السكريبت بنجاح
