'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

// Performance optimization component
export function PerformanceOptimizer() {
  const pathname = usePathname()

  useEffect(() => {
    // Preload critical resources based on current page
    const preloadCriticalResources = () => {
      // Preload fonts
      const fontLink = document.createElement('link')
      fontLink.rel = 'preload'
      fontLink.href = 'https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap'
      fontLink.as = 'style'
      fontLink.onload = () => {
        fontLink.rel = 'stylesheet'
      }
      document.head.appendChild(fontLink)

      // Preload critical images based on page
      if (pathname === '/levels' || pathname === '/') {
        preloadImage('/og-image.svg')
      }
    }

    // Optimize images with intersection observer
    const optimizeImages = () => {
      const images = document.querySelectorAll('img[data-src]')
      
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              img.src = img.dataset.src || ''
              img.classList.remove('lazy')
              imageObserver.unobserve(img)
            }
          })
        })

        images.forEach(img => imageObserver.observe(img))
      } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
          const imgElement = img as HTMLImageElement
          imgElement.src = imgElement.dataset.src || ''
        })
      }
    }

    // Prefetch next likely pages
    const prefetchNextPages = () => {
      const currentPath = pathname
      let nextPages: string[] = []

      if (currentPath === '/levels' || currentPath === '/') {
        // Prefetch common year pages
        nextPages = ['/year/1', '/year/2', '/year/3']
      } else if (currentPath.startsWith('/year/')) {
        // Prefetch common subject pages for this year
        const yearId = currentPath.split('/')[2]
        nextPages = [`/subject/${yearId}-math`, `/subject/${yearId}-science`]
      } else if (currentPath.startsWith('/subject/')) {
        // Prefetch first few lessons
        const subjectId = currentPath.split('/')[2]
        nextPages = [`/lesson/${subjectId}-1`, `/lesson/${subjectId}-2`]
      }

      nextPages.forEach(page => {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = page
        document.head.appendChild(link)
      })
    }

    // Service Worker registration for caching
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
        try {
          await navigator.serviceWorker.register('/sw.js')
          console.log('Service Worker registered successfully')
        } catch (error) {
          console.log('Service Worker registration failed:', error)
        }
      }
    }

    // Run optimizations
    preloadCriticalResources()
    optimizeImages()
    prefetchNextPages()
    registerServiceWorker()

    // Cleanup function
    return () => {
      // Remove prefetch links when component unmounts
      const prefetchLinks = document.querySelectorAll('link[rel="prefetch"]')
      prefetchLinks.forEach(link => link.remove())
    }
  }, [pathname])

  return null
}

// Helper function to preload images
function preloadImage(src: string) {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = src
  link.as = 'image'
  document.head.appendChild(link)
}

// Critical CSS inliner
export function CriticalCSS() {
  useEffect(() => {
    // Inline critical CSS for above-the-fold content
    const criticalCSS = `
      .arabic-heading {
        font-family: 'Tajawal', Arial, sans-serif;
        font-weight: 700;
        line-height: 1.2;
      }
      .arabic-text {
        font-family: 'Tajawal', Arial, sans-serif;
        direction: rtl;
        text-align: right;
      }
      .level-card {
        background: hsl(var(--card));
        border: 1px solid hsl(var(--border));
        border-radius: 0.5rem;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
      }
      .level-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    `

    const style = document.createElement('style')
    style.textContent = criticalCSS
    document.head.appendChild(style)

    return () => {
      style.remove()
    }
  }, [])

  return null
}

// Resource hints component
export function ResourceHints() {
  useEffect(() => {
    // Add DNS prefetch for external domains
    const domains = [
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'ckjjqlbzflnxolflixkq.supabase.co',
      'www.google-analytics.com'
    ]

    domains.forEach(domain => {
      const link = document.createElement('link')
      link.rel = 'dns-prefetch'
      link.href = `//${domain}`
      document.head.appendChild(link)
    })

    // Add preconnect for critical domains
    const criticalDomains = [
      'fonts.googleapis.com',
      'ckjjqlbzflnxolflixkq.supabase.co'
    ]

    criticalDomains.forEach(domain => {
      const link = document.createElement('link')
      link.rel = 'preconnect'
      link.href = `//${domain}`
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
  }, [])

  return null
}

// Lazy loading component for below-the-fold content
interface LazyLoadProps {
  children: React.ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
}

export function LazyLoad({ 
  children, 
  threshold = 0.1, 
  rootMargin = '50px',
  className = ''
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [ref, setRef] = useState<HTMLDivElement | null>(null)

  useEffect(() => {
    if (!ref) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.unobserve(ref)
        }
      },
      { threshold, rootMargin }
    )

    observer.observe(ref)

    return () => {
      if (ref) observer.unobserve(ref)
    }
  }, [ref, threshold, rootMargin])

  return (
    <div ref={setRef} className={className}>
      {isVisible ? children : <div className="h-32 bg-muted animate-pulse rounded" />}
    </div>
  )
}


