# دليل نقل الامتحانات إلى ملخص الدروس
# Migration Guide: Exams to Lesson Summaries

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نقل بيانات الامتحانات إلى ملخص الدروس في قاعدة بيانات Supabase.

## 🎯 الهدف

تحويل جدول `exams` إلى جدول `summaries` مع الحفاظ على جميع البيانات وتحديث المراجع في التطبيق.

## 📁 الملفات المطلوبة

### 1. `migrate-exams-to-summaries.sql`
سكريبت SQL شامل لإنشاء الجدول الجديد ونقل البيانات.

### 2. `migrate-exams-to-summaries.mjs`
سكريبت Node.js لتنفيذ عملية النقل مع التحقق من الأخطاء.

## 🚀 خطوات التنفيذ

### الطريقة الأولى: استخدام SQL Editor (مُوصى بها)

1. **افتح Supabase Dashboard**
   - اذهب إلى [supabase.com](https://supabase.com)
   - ادخل إلى مشروعك

2. **افتح SQL Editor**
   - من القائمة الجانبية، اختر "SQL Editor"

3. **تشغيل السكريبت**
   - انسخ محتوى ملف `migrate-exams-to-summaries.sql`
   - الصقه في SQL Editor
   - اضغط "Run" لتنفيذ السكريبت

4. **التحقق من النتائج**
   - ستظهر رسائل تأكيد نجاح العملية
   - تحقق من إنشاء جدول `summaries`
   - تأكد من نقل البيانات

### الطريقة الثانية: استخدام Node.js Script

1. **تثبيت المتطلبات**
   ```bash
   cd scripts
   npm install @supabase/supabase-js
   ```

2. **تشغيل السكريبت**
   ```bash
   node migrate-exams-to-summaries.mjs
   ```

3. **متابعة النتائج**
   - السكريبت سيعرض تقدم العملية
   - سيتم عرض النتائج النهائية

## 📊 ما يحدث أثناء النقل

### 1. إنشاء جدول summaries
```sql
CREATE TABLE public.summaries (
    id text PRIMARY KEY,
    lesson_id text REFERENCES lessons(id),
    hint text,
    exercise_image_url text,
    solution_image_url text,
    -- ... باقي الحقول
);
```

### 2. نقل البيانات
- نسخ جميع البيانات من `exams` إلى `summaries`
- الحفاظ على جميع المعرفات والروابط

### 3. تحديث نوع المحتوى
```sql
UPDATE lessons 
SET content_type = 'summary' 
WHERE content_type = 'exam';
```

### 4. إعداد الأمان
- تفعيل Row Level Security
- إنشاء سياسات الوصول المناسبة

## ✅ التحقق من نجاح النقل

### 1. فحص الجداول
```sql
-- عدد عناصر ملخص الدروس
SELECT COUNT(*) FROM summaries;

-- الدروس المحدثة
SELECT title, content_type FROM lessons WHERE content_type = 'summary';
```

### 2. اختبار التطبيق
- تصفح الموقع والتأكد من عمل ملخص الدروس
- التحقق من عرض البيانات بشكل صحيح

## 🗑️ تنظيف البيانات القديمة

بعد التأكد من نجاح النقل، يمكن حذف جدول `exams`:

```sql
-- احذف هذا فقط بعد التأكد من نجاح النقل
DROP TABLE IF EXISTS public.exams;
```

## ⚠️ احتياطات مهمة

### 1. النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات قبل البدء
- احتفظ بنسخة من جدول `exams` حتى التأكد من النجاح

### 2. اختبار البيئة
- اختبر السكريبت في بيئة التطوير أولاً
- تأكد من عمل التطبيق بشكل صحيح

### 3. التوقيت
- نفذ العملية في وقت قليل الاستخدام
- أعلم المستخدمين عن فترة الصيانة

## 🔧 استكشاف الأخطاء

### خطأ: "table summaries does not exist"
**الحل:** تشغيل السكريبت SQL أولاً لإنشاء الجدول

### خطأ: "permission denied"
**الحل:** التأكد من صلاحيات المستخدم في Supabase

### خطأ: "foreign key constraint"
**الحل:** التأكد من وجود الدروس المرتبطة في جدول `lessons`

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من logs Supabase
2. راجع رسائل الخطأ في Console
3. تأكد من صحة إعدادات قاعدة البيانات

## 🎉 النتيجة النهائية

بعد إكمال النقل بنجاح:
- ✅ جدول `summaries` جديد مع جميع البيانات
- ✅ تحديث نوع المحتوى في `lessons`
- ✅ التطبيق يعمل مع ملخص الدروس بدلاً من الامتحانات
- ✅ الحفاظ على جميع الروابط والمراجع
