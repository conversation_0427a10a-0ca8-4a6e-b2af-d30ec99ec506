'use client'

import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { Level, Year } from "@/data/types";
import { useState, useRef, useEffect } from "react";

interface HeaderDropdownProps {
  title: string;
  levels: Level[];
  yearsByLevel: Record<string, Year[]>;
  loading: boolean;
  toggleMobileMenu?: () => void;
}

// Function to sort years by grade number
const sortYearsByGrade = (years: Year[]): Year[] => {
  return [...years].sort((a, b) => {
    // Map grade names to numeric values for sorting
    const getGradeValue = (name: string): number => {
      // Primary grades
      if (name.includes('الأول') && name.includes('ابتدائي')) return 1;
      if (name.includes('الثاني') && name.includes('ابتدائي')) return 2;
      if (name.includes('الثالث') && name.includes('ابتدائي')) return 3;
      if (name.includes('الرابع') && name.includes('ابتدائي')) return 4;
      if (name.includes('الخامس') && name.includes('ابتدائي')) return 5;
      if (name.includes('السادس') && name.includes('ابتدائي')) return 6;

      // Middle grades
      if (name.includes('الأول') && name.includes('إعدادي')) return 7;
      if (name.includes('الثاني') && name.includes('إعدادي')) return 8;
      if (name.includes('الثالث') && name.includes('إعدادي')) return 9;

      // High school - new structure
      if (name.includes('جذع مشترك')) return 10;
      if (name.includes('الأولى باك')) return 11;
      if (name.includes('الثانية باك')) return 12;

      // Fallback to extract numbers
      const match = name.match(/(\d+)/);
      return match ? parseInt(match[1]) : 999; // Default high value for unknown
    };

    return getGradeValue(a.name) - getGradeValue(b.name);
  });
};

const HeaderDropdown = ({
  title,
  levels,
  yearsByLevel,
  loading,
  toggleMobileMenu,
}: HeaderDropdownProps) => {
  const isMobile = !!toggleMobileMenu;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // تصميم للشاشات الكبيرة
  if (!isMobile) {
    return (
      <div
        className="relative group"
        ref={dropdownRef}
      >
        <button
          className="flex items-center text-white hover:text-gray-300 whitespace-nowrap text-base font-medium px-2 py-1"
          onClick={toggleDropdown}
        >
          {title}
          <ChevronDown className="h-4 w-4 ml-1" />
        </button>
        <div
          className="rtl-dropdown-content max-w-[calc(100vw-2rem)] max-h-[60vh] overflow-y-auto hidden"
          style={{ display: isOpen ? 'block' : 'none' }}
        >
          {!loading &&
            levels.map((level) => (
              <div key={level.id} className="py-1">
                {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                  <Link
                    key={year.id}
                    href={`/year/${year.id}`}
                    className="block px-3 py-2 hover:bg-primary hover:text-white text-foreground rounded-md mx-1 my-1 transition-colors text-sm font-medium truncate"
                  >
                    {year.name}
                  </Link>
                ))}
              </div>
            ))}
          {loading && <div className="p-2 text-sm">جاري التحميل...</div>}
        </div>
      </div>
    );
  }

  // تصميم للهاتف المحمول
  return (
    <div>
      <div className="px-4 py-1 font-medium">{title}</div>
      {!loading &&
        levels.map((level) => (
          <div key={level.id} className="py-1 px-2">
            {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
              <Link
                key={year.id}
                href={`/year/${year.id}`}
                className="block px-4 py-2 hover:bg-accent text-foreground hover:text-accent-foreground text-sm"
                onClick={toggleMobileMenu}
              >
                {year.name}
              </Link>
            ))}
          </div>
        ))}
    </div>
  );
};

export default HeaderDropdown;
