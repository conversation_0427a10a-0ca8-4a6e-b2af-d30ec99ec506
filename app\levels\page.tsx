import { Suspense } from 'react'
import { Metadata } from 'next'
import LevelsClient from './levels-client'


export const metadata: Metadata = {
  title: 'المراحل الدراسية',
  description: 'اختر المرحلة الدراسية المناسبة لك من الابتدائي إلى الباكالوريا وفقاً للمناهج المغربية',
  keywords: 'مراحل دراسية مغربية، ابتدائي المغرب، إعدادي المغرب، ثانوي المغرب، جذع مشترك، باكالوريا مغربية، تعليم مغربي',
  openGraph: {
    title: 'المراحل الدراسية',
    description: 'اختر المرحلة الدراسية المناسبة لك من الابتدائي إلى الباكالوريا وفقاً للمناهج المغربية',
    type: 'website',
  },
  robots: {
    index: true,
    follow: true,
  },
}

// تحسين الأداء مع Static Generation
export const revalidate = 1800 // إعادة التحديث كل 30 دقيقة

// Loading component محسن
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center bg-background">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      <p className="text-muted-foreground animate-pulse">جاري تحميل المراحل الدراسية...</p>
    </div>
  </div>
)

export default function LevelsPage() {
  return (
    <>
      <Suspense fallback={<PageLoader />}>
        <LevelsClient />
      </Suspense>
    </>
  )
}
