# دليل رفع المشروع على cPanel

## 📋 المتطلبات الأساسية

### 1. متطلبات الخادم
- **Node.js**: الإصدار 18.17.0 أو أحدث
- **npm**: الإصدار 9.0.0 أو أحدث
- **Memory**: 512MB على الأقل
- **Storage**: 1GB على الأقل

### 2. إعدادات cPanel
- تفعيل **Node.js App** في cPanel
- الوصول إلى **Terminal** أو **SSH**
- قاعدة بيانات **Supabase** جاهزة

## 🚀 خطوات النشر

### الخطوة 1: تحضير الملفات
```bash
# 1. بناء المشروع للإنتاج
npm run build

# 2. إنشاء أرشيف للرفع
tar -czf arab-edu-exercises.tar.gz \
  .next/ \
  public/ \
  node_modules/ \
  package.json \
  package-lock.json \
  server.js \
  next.config.js \
  --exclude=node_modules/.cache
```

### الخطوة 2: رفع الملفات إلى cPanel
1. **رفع الأرشيف**:
   - اذهب إلى **File Manager** في cPanel
   - انتقل إلى مجلد `public_html` أو المجلد المخصص للتطبيق
   - ارفع ملف `arab-edu-exercises.tar.gz`
   - استخرج الملفات

2. **أو استخدام Git** (إذا كان متاحاً):
   ```bash
   git clone https://github.com/your-username/arab-edu-exercises.git
   cd arab-edu-exercises
   ```

### الخطوة 3: إعداد Node.js App في cPanel
1. اذهب إلى **Node.js App** في cPanel
2. اضغط على **Create App**
3. املأ البيانات التالية:
   - **Node.js version**: 18.17.0 أو أحدث
   - **Application mode**: Production
   - **Application root**: مسار مجلد التطبيق
   - **Application URL**: النطاق أو النطاق الفرعي
   - **Application startup file**: `server.js`

### الخطوة 4: إعداد متغيرات البيئة
1. في إعدادات **Node.js App**، اضغط على **Environment variables**
2. أضف المتغيرات التالية:
   ```
   NODE_ENV=production
   PORT=3000
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### الخطوة 5: تثبيت التبعيات
```bash
# في Terminal أو SSH
cd /path/to/your/app
npm install --production
```

### الخطوة 6: بدء التطبيق
1. في **Node.js App**، اضغط على **Start App**
2. أو استخدم Terminal:
   ```bash
   npm start
   ```

## 🔧 إعدادات إضافية

### إعداد SSL Certificate
1. اذهب إلى **SSL/TLS** في cPanel
2. فعّل **Let's Encrypt** للنطاق
3. تأكد من إعادة توجيه HTTP إلى HTTPS

### إعداد .htaccess (اختياري)
إنشاء ملف `.htaccess` في `public_html`:
```apache
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه جميع الطلبات إلى Node.js
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://localhost:3000/$1 [P,L]
```

### مراقبة الأداء
```bash
# فحص حالة التطبيق
curl http://yourdomain.com/health

# مراقبة السجلات
tail -f /path/to/app/logs/app.log
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "Cannot find module"
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### 2. خطأ في الذاكرة
```bash
# زيادة حد الذاكرة
export NODE_OPTIONS="--max-old-space-size=2048"
npm start
```

#### 3. مشكلة في المنفذ
- تأكد من أن المنفذ 3000 متاح
- أو غيّر المنفذ في متغيرات البيئة

#### 4. مشكلة في Supabase
- تحقق من صحة `SUPABASE_URL` و `SUPABASE_ANON_KEY`
- تأكد من إعدادات CORS في Supabase

## 📊 مراقبة الأداء

### فحص حالة التطبيق
```bash
# فحص الصحة العامة
curl http://yourdomain.com/health

# فحص استهلاك الذاكرة
ps aux | grep node

# فحص استخدام المعالج
top -p $(pgrep node)
```

### تحسين الأداء
1. **تفعيل Gzip**: مفعّل تلقائياً في `server.js`
2. **Cache Headers**: مُعدّة للملفات الثابتة
3. **CDN**: استخدم Cloudflare أو مشابه
4. **Database Optimization**: فهرسة قاعدة البيانات

## 🔄 التحديثات

### تحديث التطبيق
```bash
# 1. إيقاف التطبيق
npm stop

# 2. سحب التحديثات
git pull origin main

# 3. تثبيت التبعيات الجديدة
npm install

# 4. بناء المشروع
npm run build

# 5. بدء التطبيق
npm start
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجلات الأخطاء في cPanel
2. راجع ملف `TROUBLESHOOTING.md`
3. تواصل مع دعم cPanel إذا لزم الأمر

---

**ملاحظة**: تأكد من عمل نسخة احتياطية قبل أي تحديث أو تغيير!
