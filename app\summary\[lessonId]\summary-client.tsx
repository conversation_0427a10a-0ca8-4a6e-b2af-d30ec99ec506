'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLesson, useSubject, useYear, useLevel } from '@/hooks/use-education-data';
import SummaryList from '@/components/summary/SummaryList';
import { useViewerMode } from '@/hooks/use-viewer-mode';

interface SummaryClientProps {
  lessonId: string;
}

const SummaryClient = ({ lessonId }: SummaryClientProps) => {
  const { isViewerMode } = useViewerMode();
  const searchParams = useSearchParams();

  // الحصول على رابط العودة من معاملات URL
  const returnTo = searchParams.get('returnTo');

  // استخدام React Query hooks للحصول على البيانات
  const { data: lesson, isLoading: lessonLoading } = useLesson(lessonId);
  const { data: subject, isLoading: subjectLoading } = useSubject(lesson?.subjectId || '');
  const { data: year, isLoading: yearLoading } = useYear(subject?.yearId || '');
  const { data: level, isLoading: levelLoading } = useLevel(year?.levelId || '');

  const loading = lessonLoading || subjectLoading || yearLoading || levelLoading;



  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg">جاري تحميل البيانات...</p>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <h1 className="text-3xl font-bold mb-6 text-primary">لم يتم العثور على ملخص الدرس</h1>
          <Link href="/levels" className="text-primary hover:underline">
            العودة إلى المستويات الدراسية
          </Link>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  console.log(`عدد عناصر ملخص الدرس المتاحة في "${lesson.title}": ${lesson.exercises?.length || 0}`);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {!isViewerMode && <Header />}

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">{lesson.title}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            {lesson.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground">
            <Link href="/levels" className="hover:text-primary transition-colors">
              المستويات
            </Link>
            <span>/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors">
                  {year.name}
                </Link>
                <span>/</span>
              </>
            )}
            {subject && (
              <>
                <Link href={returnTo || `/subject/${subject.id}`} className="hover:text-primary transition-colors">
                  {subject.name}
                </Link>
                <span>/</span>
              </>
            )}
            <span className="text-foreground">{lesson.title}</span>
          </nav>
        </div>

        {/* Summary Exercises */}
        <SummaryList
          exercises={lesson.exercises}
          lessonTitle={lesson.title}
          subjectName={subject?.name || ''}
          yearName={year?.name || ''}
          levelName={level?.name || ''}
        />
      </div>

      {!isViewerMode && (
        <div className="mt-auto">
          <Footer />
        </div>
      )}
    </div>
  );
};

export default SummaryClient;
