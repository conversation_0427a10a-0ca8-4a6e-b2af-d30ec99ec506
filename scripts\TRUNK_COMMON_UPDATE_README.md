# تحديث بنية جذع مشترك

## نظرة عامة

هذا السكريبت يقوم بتحديث بنية "جذع مشترك" في قاعدة البيانات لتحويلها من مستوى واحد عام إلى ثلاثة تخصصات منفصلة:

1. **العلوم** - التخصص العلمي
2. **التكنولوجيا** - التخصص التقني  
3. **الآداب والعلوم الإنسانية** - التخصص الأدبي

## الملفات المطلوبة

### 1. `update-trunk-common-structure.sql`
السكريبت الرئيسي لتحديث قاعدة البيانات في Supabase.

## التغييرات التي سيتم تطبيقها

### 1. حذف البنية القديمة
- حذف السنة الحالية `trunk_common_year`
- حذف جميع المواد المرتبطة بها

### 2. إضافة البنية الجديدة
- تحديث مستوى `trunk_common` ليحتوي على 3 سنوات بدلاً من واحدة
- إضافة السنوات الثلاث الجديدة:
  - `trunk_common_sciences` - الجذع المشترك - العلوم
  - `trunk_common_technology` - الجذع المشترك - التكنولوجيا  
  - `trunk_common_humanities` - الجذع المشترك - الآداب والعلوم الإنسانية

### 3. إضافة المواد الدراسية

#### التخصص العلمي (العلوم) - 10 مواد:
- الرياضيات، الفيزياء، الكيمياء، علوم الحياة والأرض
- اللغة العربية، اللغة الفرنسية، اللغة الإنجليزية
- التاريخ والجغرافيا، الفلسفة، التربية الإسلامية

#### التخصص التقني (التكنولوجيا) - 10 مواد:
- الرياضيات، الفيزياء، الكيمياء
- التكنولوجيا الصناعية، الهندسة الميكانيكية، الهندسة الكهربائية
- اللغة العربية، اللغة الفرنسية، اللغة الإنجليزية، التربية الإسلامية

#### التخصص الأدبي (الآداب والعلوم الإنسانية) - 10 مواد:
- اللغة العربية، اللغة الفرنسية، اللغة الإنجليزية
- التاريخ والجغرافيا، الفلسفة، التربية الإسلامية
- علم الاجتماع، علم النفس، الأدب العربي، الرياضيات

## كيفية التطبيق

### الطريقة 1: استخدام Supabase SQL Editor (الموصى بها)

1. افتح Supabase Dashboard
2. انتقل إلى SQL Editor
3. انسخ محتوى ملف `update-trunk-common-structure.sql`
4. الصق المحتوى في المحرر
5. اضغط على "Run" لتنفيذ السكريبت

### الطريقة 2: استخدام سطر الأوامر

```bash
# إذا كان لديك psql مثبت
psql -h your-supabase-host -U postgres -d postgres -f update-trunk-common-structure.sql
```

## التحقق من النتائج

السكريبت يتضمن استعلامات للتحقق من نجاح التحديث:

1. **المستوى المحدث**: يعرض تفاصيل مستوى جذع مشترك الجديد
2. **السنوات الجديدة**: يعرض السنوات الثلاث المضافة
3. **المواد الدراسية**: يعرض جميع المواد الجديدة مرتبة حسب التخصص
4. **الإحصائيات**: يعرض عدد السنوات والمواد الإجمالي

## النتائج المتوقعة

بعد تنفيذ السكريبت بنجاح:

- **3 سنوات جديدة** في مستوى جذع مشترك
- **30 مادة دراسية** موزعة على التخصصات الثلاثة (10 مواد لكل تخصص)
- **تحديث العلاقات** بين الجداول بشكل صحيح

## ملاحظات مهمة

⚠️ **تحذير**: هذا السكريبت سيحذف البيانات الحالية لجذع مشترك. تأكد من عمل نسخة احتياطية قبل التنفيذ.

✅ **التوافق**: السكريبت متوافق مع الكود الحالي حيث تم تحديث الملفات المرتبطة:
- `src/backend/data/levels.ts`
- `src/backend/data/years.ts`

🔄 **التحديث التلقائي**: البيانات ستظهر تلقائياً في التطبيق بعد تنفيذ السكريبت لأن التطبيق يجلب البيانات من Supabase مباشرة.
