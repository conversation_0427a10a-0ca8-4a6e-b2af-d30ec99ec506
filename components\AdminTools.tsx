'use client';

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, RefreshCw } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { getLevels, getYearsForLevel, getSubjectsForYear, getLessonsForSubject } from "@/data/educationData";

function AdminTools() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const refreshDataFromSupabase = async () => {
    setIsLoading(true);
    setMessage('');
    try {
      // نحاول إعادة تحميل البيانات
      const levels = await getLevels();
      console.log(`تم تحميل ${levels.length} مستويات`);

      setMessage("تم تحديث البيانات بنجاح");
    } catch (error) {
      console.error("خطأ في تحديث البيانات:", error);
      setMessage(`خطأ في تحديث البيانات: ${error instanceof Error ? error.message : "حدث خطأ غير متوقع"}`);
    } finally {
      setIsLoading(false);
    }
  };

  const uploadDataToSupabase = async () => {
    setIsLoading(true);
    setMessage('');
    try {
      // جلب البيانات من مصدر البيانات الحالي
      const levels = await getLevels();
      console.log("تم جلب البيانات:", {
        levels: levels.length
      });

      setMessage("تم جلب البيانات بنجاح. يمكن إضافة المزيد من الوظائف هنا.");
    } catch (error) {
      console.error("خطأ في رفع البيانات:", error);
      setMessage(`خطأ في رفع البيانات: ${error instanceof Error ? error.message : "حدث خطأ غير متوقع"}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>أدوات الإدارة</CardTitle>
        <CardDescription>أدوات لإدارة البيانات في النظام</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button
          onClick={refreshDataFromSupabase}
          disabled={isLoading}
          className="w-full mb-2"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              جاري تحديث البيانات...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              تحديث البيانات
            </>
          )}
        </Button>

        <Button
          onClick={uploadDataToSupabase}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              جاري معالجة البيانات...
            </>
          ) : (
            <>
              <Loader2 className="mr-2 h-4 w-4" />
              معالجة البيانات
            </>
          )}
        </Button>

        {message && (
          <div className={`p-3 rounded-md text-sm ${
            message.includes('خطأ') 
              ? 'bg-destructive/10 text-destructive border border-destructive/20' 
              : 'bg-green-50 text-green-700 border border-green-200'
          }`}>
            {message}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default AdminTools;
