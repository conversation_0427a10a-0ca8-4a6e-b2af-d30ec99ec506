/**
 * RTL (Right-to-Left) utility functions and classes for Arabic language support
 */

import React from 'react';
import { cn } from './utils';

/**
 * RTL-aware class names for common layout patterns
 */
export const rtlClasses = {
  // Flexbox utilities
  flexRow: 'flex flex-row-reverse',
  flexRowNormal: 'flex flex-row',
  spaceX: 'space-x-reverse space-x-4',
  spaceXSm: 'space-x-reverse space-x-2',
  spaceXLg: 'space-x-reverse space-x-6',
  
  // Text alignment
  textRight: 'text-right',
  textLeft: 'text-left',
  textCenter: 'text-center',
  
  // Margins and padding (RTL-aware)
  ml: 'mr-auto',
  mr: 'ml-auto',
  pl: 'pr-4',
  pr: 'pl-4',
  
  // Positioning
  left: 'right-0',
  right: 'left-0',
  
  // Borders
  borderL: 'border-r',
  borderR: 'border-l',
  
  // Common component classes
  card: 'bg-card text-right p-6 rounded-lg shadow-sm border border-muted/50',
  button: 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium',
  input: 'w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-right',
  dropdown: 'absolute top-full right-0 mt-2 w-48 bg-card border border-primary/20 rounded-md shadow-md z-50',
  navigation: 'flex items-center space-x-reverse space-x-4',
  breadcrumb: 'flex items-center space-x-reverse space-x-2 text-sm text-muted-foreground',
};

/**
 * RTL-aware utility function to combine classes
 */
export function rtlCn(...classes: (string | undefined | null | false)[]): string {
  return cn(...classes);
}

/**
 * Get RTL-appropriate margin/padding classes
 */
export function getRtlSpacing(direction: 'left' | 'right', size: string = '4'): string {
  if (direction === 'left') {
    return `mr-${size}`;
  }
  return `ml-${size}`;
}

/**
 * Get RTL-appropriate positioning classes
 */
export function getRtlPosition(position: 'left' | 'right'): string {
  return position === 'left' ? 'right-0' : 'left-0';
}

/**
 * Check if text contains Arabic characters
 */
export function isArabicText(text: string): boolean {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
}

/**
 * Get appropriate text direction based on content
 */
export function getTextDirection(text: string): 'rtl' | 'ltr' {
  return isArabicText(text) ? 'rtl' : 'ltr';
}

/**
 * RTL-aware icon rotation classes
 */
export const rtlIconClasses = {
  arrow: 'rtl:rotate-180',
  chevron: 'rtl:rotate-180',
  normal: '',
};

/**
 * Get RTL-appropriate flex direction
 */
export function getRtlFlexDirection(reverse: boolean = false): string {
  return reverse ? 'flex-row-reverse' : 'flex-row';
}

/**
 * Common Arabic typography classes
 */
export const arabicTypography = {
  text: 'arabic-text',
  paragraph: 'arabic-paragraph',
  heading: 'arabic-heading',
  enhanced: 'text-enhanced',
};

/**
 * RTL-aware component wrapper factory
 * Returns a function that can be used to create RTL-aware components
 */
export function withRtl<T extends { className?: string }>(
  Component: React.ComponentType<T>
): React.ComponentType<T & { rtl?: boolean }> {
  const RtlWrapper = (props: T & { rtl?: boolean }) => {
    const { rtl = true, className, ...rest } = props;
    const rtlClass = rtl ? 'rtl-container' : '';
    const combinedClassName = cn(rtlClass, className);

    return React.createElement(Component, {
      ...(rest as T),
      className: combinedClassName,
    });
  };

  RtlWrapper.displayName = `withRtl(${Component.displayName || Component.name || 'Component'})`;

  return RtlWrapper;
}

/**
 * RTL-aware grid classes
 */
export const rtlGridClasses = {
  cols1: 'grid-cols-1',
  cols2: 'grid-cols-1 md:grid-cols-2',
  cols3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  cols4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  gap: 'gap-6',
  gapSm: 'gap-4',
  gapLg: 'gap-8',
};

/**
 * RTL-aware animation classes
 */
export const rtlAnimationClasses = {
  slideInRight: 'animate-in slide-in-from-right-4',
  slideInLeft: 'animate-in slide-in-from-left-4',
  slideOutRight: 'animate-out slide-out-to-right-4',
  slideOutLeft: 'animate-out slide-out-to-left-4',
};

/**
 * Helper function to get appropriate slide animation for RTL
 */
export function getRtlSlideAnimation(direction: 'in' | 'out', side: 'start' | 'end'): string {
  if (direction === 'in') {
    return side === 'start' ? rtlAnimationClasses.slideInRight : rtlAnimationClasses.slideInLeft;
  }
  return side === 'start' ? rtlAnimationClasses.slideOutRight : rtlAnimationClasses.slideOutLeft;
}

/**
 * RTL-aware form classes
 */
export const rtlFormClasses = {
  label: 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right',
  input: 'rtl-input',
  button: 'rtl-button',
  fieldset: 'space-y-4 text-right',
  group: 'space-y-2',
};

/**
 * Export all RTL utilities as a single object
 */
export const rtlUtils = {
  classes: rtlClasses,
  cn: rtlCn,
  spacing: getRtlSpacing,
  position: getRtlPosition,
  isArabic: isArabicText,
  textDirection: getTextDirection,
  icons: rtlIconClasses,
  flexDirection: getRtlFlexDirection,
  typography: arabicTypography,
  grid: rtlGridClasses,
  animations: rtlAnimationClasses,
  slideAnimation: getRtlSlideAnimation,
  form: rtlFormClasses,
  withRtl,
};

export default rtlUtils;





