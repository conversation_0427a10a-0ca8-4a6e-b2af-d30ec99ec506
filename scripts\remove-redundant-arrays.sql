-- سكريبت إزالة المصفوفات المكررة من قاعدة البيانات
-- Script to Remove Redundant Arrays from Database Tables

-- هذا السكريبت يزيل الحقول المكررة التي لم تعد مستخدمة في التطبيق:
-- 1. levels.years - مصفوفة السنوات في جدول المستويات
-- 2. years.subjects - مصفوفة المواد في جدول السنوات  
-- 3. subjects.lessons - مصفوفة الدروس في جدول المواد

-- ملاحظة: هذه الحقول مكررة لأن العلاقات موجودة بالفعل عبر foreign keys:
-- - years.level_id يربط السنوات بالمستويات
-- - subjects.year_id يربط المواد بالسنوات
-- - lessons.subject_id يربط الدروس بالمواد

BEGIN;

-- 1. إزالة حقل years من جدول levels
ALTER TABLE public.levels 
DROP COLUMN IF EXISTS years;

-- 2. إزالة حقل subjects من جدول years
ALTER TABLE public.years 
DROP COLUMN IF EXISTS subjects;

-- 3. إزالة حقل lessons من جدول subjects
ALTER TABLE public.subjects 
DROP COLUMN IF EXISTS lessons;

-- تأكيد التغييرات
COMMIT;

-- عرض هيكل الجداول بعد التحديث
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('levels', 'years', 'subjects', 'lessons')
ORDER BY table_name, ordinal_position;
