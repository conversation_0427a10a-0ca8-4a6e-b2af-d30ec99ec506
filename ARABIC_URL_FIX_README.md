# إصلاح مشكلة الروابط العربية في تحميل ملفات PDF

## 🔍 المشكلة الأصلية

عندما يتم تحميل ملف PDF من رابط يحتوي على أسماء ملفات باللغة العربية مثل:
```
https://talamidi.com/Cours/Maroc/College/3AC/Examens%20Re/Examen%202022/اجتماعيات/Talamidi.com_اجتماعيات-جهة%20طنجة%20تطوان-10.pdf
```

كانت المشكلة أن:
- اسم الملف المحمل لا يظهر بالعربية بشكل صحيح
- الأحرف العربية تظهر مُرمزة (URL encoded) مثل `%D8%A7%D8%AC%D8%AA%D9%85%D8%A7%D8%B9%D9%8A%D8%A7%D8%AA`
- الملف المحمل قد لا يعمل بشكل صحيح

## 🛠️ الحلول المطبقة

### 1. إنشاء دوال مساعدة للتعامل مع الروابط العربية

تم إنشاء ملف `utils/arabic-url-utils.ts` يحتوي على:

#### `decodeArabicFilename(encodedFilename: string)`
- فك ترميز الأحرف العربية من URL encoding
- معالجة خاصة للأحرف العربية الشائعة
- معالجة الأخطاء والعودة للطرق البديلة

#### `extractFilenameFromUrl(url: string, defaultName: string)`
- استخراج اسم الملف من الرابط
- فك ترميز الأحرف العربية
- تنظيف اسم الملف ليكون صالحاً لأنظمة الملفات
- إنشاء اسم افتراضي بالعربية إذا فشل الاستخراج

#### `encodeArabicUrl(url: string)`
- ترميز الروابط بشكل صحيح للاستخدام في عارضات PDF
- معالجة الأخطاء في الترميز

#### `sanitizeFilename(filename: string)`
- تنظيف أسماء الملفات من الأحرف غير المسموحة
- الحفاظ على الأحرف العربية

### 2. تحديث hook معالجة PDF

تم تحديث `hooks/use-pdf-handler.ts`:
- استخدام الدوال الجديدة لاستخراج أسماء الملفات
- تبسيط الكود وتحسين الأداء
- أسماء افتراضية بالعربية

### 3. تحديث مكون عارض PDF

تم تحديث `components/ui/pdf-viewer.tsx`:
- استخدام الدوال الجديدة لترميز الروابط
- تحسين معالجة أسماء الملفات العربية
- أسماء افتراضية بالعربية

## 🧪 صفحة الاختبار

تم إنشاء صفحة اختبار في `/test-arabic` تتيح:
- اختبار فك ترميز أسماء الملفات العربية
- اختبار تحميل الملفات
- عرض النتائج بوضوح
- أمثلة متنوعة للاختبار

### كيفية الاختبار:
1. انتقل إلى `http://localhost:3000/test-arabic`
2. أدخل رابط PDF يحتوي على أحرف عربية
3. اضغط "اختبار فك الترميز" لرؤية النتائج
4. اضغط "اختبار التحميل" لاختبار التحميل الفعلي

## 📋 الميزات الجديدة

### ✅ فك ترميز شامل للأحرف العربية
- جميع الأحرف العربية الأساسية (28 حرف)
- الهمزات بأشكالها المختلفة
- التشكيل (الحركات)
- الأرقام العربية
- علامات الترقيم الشائعة

### ✅ معالجة أخطاء محسنة
- طرق بديلة متعددة لفك الترميز
- معالجة الأخطاء بدون توقف التطبيق
- رسائل تحذيرية واضحة في وحدة التحكم

### ✅ أسماء ملفات عربية
- أسماء افتراضية بالعربية ("مستند" بدلاً من "document")
- الحفاظ على الأسماء العربية الأصلية
- تنظيف أسماء الملفات للتوافق مع أنظمة الملفات

### ✅ تحسين الأداء
- كود أكثر تنظيماً وقابلية للصيانة
- دوال قابلة لإعادة الاستخدام
- تقليل التكرار في الكود

## 🔧 الملفات المحدثة

1. **`utils/arabic-url-utils.ts`** - جديد
   - دوال مساعدة للتعامل مع الروابط العربية

2. **`hooks/use-pdf-handler.ts`** - محدث
   - استخدام الدوال الجديدة
   - تبسيط منطق استخراج أسماء الملفات

3. **`components/ui/pdf-viewer.tsx`** - محدث
   - تحسين ترميز الروابط
   - معالجة أفضل لأسماء الملفات العربية

4. **`app/test-arabic/page.tsx`** - جديد
   - صفحة اختبار شاملة

5. **`components/ui/input.tsx`** - جديد
   - مكون Input للاستخدام في صفحة الاختبار

## 🎯 النتائج المتوقعة

بعد تطبيق هذه الحلول:
- ✅ أسماء الملفات المحملة تظهر بالعربية بشكل صحيح
- ✅ الملفات المحملة تعمل بشكل طبيعي
- ✅ تحسين تجربة المستخدم العربي
- ✅ كود أكثر تنظيماً وقابلية للصيانة

## 🚀 الاستخدام

```typescript
import { extractFilenameFromUrl, decodeArabicFilename } from '@/utils/arabic-url-utils';

// استخراج اسم ملف عربي من رابط
const filename = extractFilenameFromUrl(arabicUrl, 'مستند');

// فك ترميز اسم ملف عربي
const decodedName = decodeArabicFilename(encodedFilename);
```

## 📝 ملاحظات

- الحلول متوافقة مع جميع المتصفحات الحديثة
- تم اختبار الحلول مع أمثلة حقيقية
- الكود يدعم التوسع المستقبلي
- معالجة شاملة للأخطاء المحتملة
