'use client'

import Link from 'next/link'
import { ChevronLeft, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <nav 
      aria-label="مسار التنقل" 
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
      dir="rtl"
    >
      <ol className="flex items-center space-x-1 rtl:space-x-reverse">
        {/* Home link */}
        <li>
          <Link 
            href="/levels" 
            className="flex items-center hover:text-foreground transition-colors"
            aria-label="الصفحة الرئيسية"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">الرئيسية</span>
          </Link>
        </li>

        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            <ChevronLeft className="h-4 w-4 mx-1 text-muted-foreground/50" />
            {item.href && !item.current ? (
              <Link 
                href={item.href}
                className="hover:text-foreground transition-colors"
                aria-current={item.current ? 'page' : undefined}
              >
                {item.label}
              </Link>
            ) : (
              <span 
                className={cn(
                  item.current && "text-foreground font-medium"
                )}
                aria-current={item.current ? 'page' : undefined}
              >
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

import { siteConfig } from '@/lib/config'

// Structured data for breadcrumbs
export function BreadcrumbStructuredData({ items }: { items: BreadcrumbItem[] }) {
  const baseUrl = siteConfig.url
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "الرئيسية",
        "item": `${baseUrl}/levels`
      },
      ...items.map((item, index) => ({
        "@type": "ListItem",
        "position": index + 2,
        "name": item.label,
        "item": item.href ? `${baseUrl}${item.href}` : undefined
      }))
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}

// Helper function to generate breadcrumb items
export function generateBreadcrumbItems(
  level?: { id: string; name: string },
  year?: { id: string; name: string },
  subject?: { id: string; name: string },
  lesson?: { id: string; title: string },
  currentPage?: 'homework' | 'summary' | 'exam'
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = []

  if (level) {
    items.push({
      label: level.name,
      href: '/levels'
    })
  }

  if (year) {
    items.push({
      label: year.name,
      href: `/year/${year.id}`
    })
  }

  if (subject) {
    items.push({
      label: subject.name,
      href: `/subject/${subject.id}`
    })
  }

  if (lesson) {
    items.push({
      label: lesson.title,
      href: `/lesson/${lesson.id}`
    })
  }

  if (currentPage && lesson) {
    const pageLabels = {
      homework: 'الفروض المنزلية',
      summary: 'الملخص',
      exam: 'الامتحان'
    }
    
    items.push({
      label: pageLabels[currentPage],
      current: true
    })
  }

  return items
}
