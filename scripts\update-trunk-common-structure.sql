-- سكريبت تحديث بنية جذع مشترك في Supabase
-- تحويل جذع مشترك إلى ثلاثة تخصصات: العلوم، التكنولوجيا، الآداب والعلوم الإنسانية

-- 1. حذف السنة الحالية لجذع مشترك والمواد المرتبطة بها
DELETE FROM subjects WHERE year_id = 'trunk_common_year';
DELETE FROM years WHERE id = 'trunk_common_year';

-- 2. تحديث مستوى جذع مشترك ليحتوي على التخصصات الثلاثة
UPDATE levels
SET years = ARRAY['trunk_common_sciences', 'trunk_common_technology', 'trunk_common_humanities'],
    description = 'السنة الأولى من التعليم الثانوي مع التخصصات الثلاثة'
WHERE id = 'trunk_common';

-- 3. إضافة السنوات الجديدة للتخصصات الثلاثة
INSERT INTO years (id, name, level_id, subjects) VALUES
('trunk_common_sciences', 'الجذع المشترك - العلوم', 'trunk_common', ARRAY[]::text[]),
('trunk_common_technology', 'الجذع المشترك - التكنولوجيا', 'trunk_common', ARRAY[]::text[]),
('trunk_common_humanities', 'الجذع المشترك - الآداب والعلوم الإنسانية', 'trunk_common', ARRAY[]::text[]);

-- 4. إضافة مواد التخصص العلمي (العلوم)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('trunk_sci_math', 'الرياضيات', '📐', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_physics', 'الفيزياء', '⚛️', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_chemistry', 'الكيمياء', '🧪', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_biology', 'علوم الحياة والأرض', '🌱', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_arabic', 'اللغة العربية', '📚', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_french', 'اللغة الفرنسية', '🇫🇷', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_english', 'اللغة الإنجليزية', '🇺🇸', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_history', 'التاريخ والجغرافيا', '🌍', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_philosophy', 'الفلسفة', '🤔', 'trunk_common_sciences', ARRAY[]::text[]),
('trunk_sci_islamic', 'التربية الإسلامية', '☪️', 'trunk_common_sciences', ARRAY[]::text[]);

-- 5. إضافة مواد التخصص التقني (التكنولوجيا)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('trunk_tech_math', 'الرياضيات', '📐', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_physics', 'الفيزياء', '⚛️', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_chemistry', 'الكيمياء', '🧪', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_technology', 'التكنولوجيا الصناعية', '⚙️', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_engineering', 'الهندسة الميكانيكية', '🔧', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_electronics', 'الهندسة الكهربائية', '⚡', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_arabic', 'اللغة العربية', '📚', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_french', 'اللغة الفرنسية', '🇫🇷', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_english', 'اللغة الإنجليزية', '🇺🇸', 'trunk_common_technology', ARRAY[]::text[]),
('trunk_tech_islamic', 'التربية الإسلامية', '☪️', 'trunk_common_technology', ARRAY[]::text[]);

-- 6. إضافة مواد التخصص الأدبي (الآداب والعلوم الإنسانية)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('trunk_hum_arabic', 'اللغة العربية', '📚', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_french', 'اللغة الفرنسية', '🇫🇷', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_english', 'اللغة الإنجليزية', '🇺🇸', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_history', 'التاريخ والجغرافيا', '🌍', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_philosophy', 'الفلسفة', '🤔', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_islamic', 'التربية الإسلامية', '☪️', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_sociology', 'علم الاجتماع', '👥', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_psychology', 'علم النفس', '🧠', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_literature', 'الأدب العربي', '📖', 'trunk_common_humanities', ARRAY[]::text[]),
('trunk_hum_math', 'الرياضيات', '📐', 'trunk_common_humanities', ARRAY[]::text[]);

-- 7. تحديث مصفوفة subjects في جدول years
UPDATE years SET subjects = ARRAY[
    'trunk_sci_math', 'trunk_sci_physics', 'trunk_sci_chemistry', 'trunk_sci_biology',
    'trunk_sci_arabic', 'trunk_sci_french', 'trunk_sci_english', 'trunk_sci_history',
    'trunk_sci_philosophy', 'trunk_sci_islamic'
] WHERE id = 'trunk_common_sciences';

UPDATE years SET subjects = ARRAY[
    'trunk_tech_math', 'trunk_tech_physics', 'trunk_tech_chemistry', 'trunk_tech_technology',
    'trunk_tech_engineering', 'trunk_tech_electronics', 'trunk_tech_arabic', 'trunk_tech_french',
    'trunk_tech_english', 'trunk_tech_islamic'
] WHERE id = 'trunk_common_technology';

UPDATE years SET subjects = ARRAY[
    'trunk_hum_arabic', 'trunk_hum_french', 'trunk_hum_english', 'trunk_hum_history',
    'trunk_hum_philosophy', 'trunk_hum_islamic', 'trunk_hum_sociology', 'trunk_hum_psychology',
    'trunk_hum_literature', 'trunk_hum_math'
] WHERE id = 'trunk_common_humanities';

-- 8. عرض النتائج للتأكد من التحديث
SELECT 'المستوى المحدث:' as info;
SELECT id, name, description, years FROM levels WHERE id = 'trunk_common';

SELECT 'السنوات الجديدة:' as info;
SELECT id, name, level_id FROM years WHERE level_id = 'trunk_common';

SELECT 'المواد الدراسية الجديدة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN (
    'trunk_common_sciences', 'trunk_common_technology', 'trunk_common_humanities'
) ORDER BY year_id, name;

-- 9. إحصائيات التحديث
SELECT
    'إحصائيات التحديث' as info,
    (SELECT COUNT(*) FROM years WHERE level_id = 'trunk_common') as total_years,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN ('trunk_common_sciences', 'trunk_common_technology', 'trunk_common_humanities')) as total_subjects;

-- 10. التحقق من البيانات النهائية
SELECT 'التحقق النهائي من البنية:' as final_check;

-- عرض المستوى المحدث
SELECT 'مستوى جذع مشترك:' as section, id, name, description, array_length(years, 1) as years_count
FROM levels WHERE id = 'trunk_common';

-- عرض السنوات الثلاث
SELECT 'السنوات الثلاث:' as section, id, name, level_id
FROM years WHERE level_id = 'trunk_common' ORDER BY id;

-- عرض عدد المواد لكل تخصص
SELECT
    'عدد المواد لكل تخصص:' as section,
    year_id as specialization,
    COUNT(*) as subjects_count
FROM subjects
WHERE year_id IN ('trunk_common_sciences', 'trunk_common_technology', 'trunk_common_humanities')
GROUP BY year_id
ORDER BY year_id;
