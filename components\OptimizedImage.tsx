'use client'

import Image from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  loading = 'lazy',
  onLoad,
  onError
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }

  // Generate optimized alt text if not provided
  const optimizedAlt = alt || generateAltText(src)

  // Error fallback
  if (hasError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-muted text-muted-foreground",
          className
        )}
        style={{ width, height }}
        role="img"
        aria-label={optimizedAlt}
      >
        <span className="text-sm">فشل تحميل الصورة</span>
      </div>
    )
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <div 
          className="absolute inset-0 bg-muted animate-pulse"
          style={{ width, height }}
        />
      )}
      
      <Image
        src={src}
        alt={optimizedAlt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        sizes={sizes || (fill ? '100vw' : undefined)}
        loading={priority ? 'eager' : loading}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        // SEO and accessibility attributes
        itemProp="image"
        decoding="async"
      />
    </div>
  )
}

// Generate meaningful alt text from image URL
function generateAltText(src: string): string {
  try {
    const url = new URL(src, window.location.origin)
    const filename = url.pathname.split('/').pop() || ''
    const nameWithoutExt = filename.replace(/\.[^/.]+$/, '')
    
    // Convert filename to readable Arabic text
    const readable = nameWithoutExt
      .replace(/[-_]/g, ' ')
      .replace(/\d+/g, '')
      .trim()
    
    return readable || 'صورة تعليمية'
  } catch {
    return 'صورة تعليمية'
  }
}

// Exercise image component with specific SEO optimization
interface ExerciseImageProps extends Omit<OptimizedImageProps, 'alt'> {
  exerciseTitle?: string
  subjectName?: string
  lessonTitle?: string
  type?: 'exercise' | 'solution' | 'hint'
}

export function ExerciseImage({
  exerciseTitle,
  subjectName,
  lessonTitle,
  type = 'exercise',
  ...props
}: ExerciseImageProps) {
  const typeLabels = {
    exercise: 'تمرين',
    solution: 'حل',
    hint: 'تلميح'
  }

  const altText = [
    typeLabels[type],
    exerciseTitle,
    lessonTitle && `من درس ${lessonTitle}`,
    subjectName && `في مادة ${subjectName}`
  ].filter(Boolean).join(' ')

  return (
    <OptimizedImage
      {...props}
      alt={altText}
      className={cn("rounded-lg border", props.className)}
    />
  )
}

// Subject icon component
interface SubjectIconProps {
  icon: string
  subjectName: string
  size?: number
  className?: string
}

export function SubjectIcon({ 
  icon, 
  subjectName, 
  size = 24, 
  className 
}: SubjectIconProps) {
  return (
    <span 
      className={cn("inline-flex items-center justify-center", className)}
      style={{ fontSize: size }}
      role="img"
      aria-label={`أيقونة مادة ${subjectName}`}
      title={subjectName}
    >
      {icon}
    </span>
  )
}

// Lazy loading wrapper for images below the fold
export function LazyImage(props: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      loading="lazy"
      priority={false}
    />
  )
}

// Critical image wrapper for above-the-fold images
export function CriticalImage(props: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      loading="eager"
      priority={true}
    />
  )
}
