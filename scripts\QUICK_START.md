# تعليمات سريعة - نقل الامتحانات إلى ملخص الدروس
# Quick Start - Migrate Exams to Summaries

## 🚀 التنفيذ السريع (5 دقائق)

### الخطوة 1: تشغيل السكريبت SQL
1. افتح [Supabase Dashboard](https://supabase.com/dashboard)
2. اذهب إلى مشروعك
3. افتح **SQL Editor** من القائمة الجانبية
4. انسخ والصق محتوى ملف `migrate-exams-to-summaries.sql`
5. اضغط **Run** لتنفيذ السكريبت

### الخطوة 2: التحقق من النجاح
```sql
-- تحقق من إنشاء الجدول
SELECT COUNT(*) as total_summaries FROM summaries;

-- تحقق من تحديث الدروس
SELECT COUNT(*) as summary_lessons FROM lessons WHERE content_type = 'summary';
```

### الخطوة 3: اختبار التطبيق
- تصفح الموقع
- اذهب إلى أي مادة دراسية
- تحقق من ظهور تبويب "ملخص الدروس" بدلاً من "الامتحانات"

## 🔧 الطريقة البديلة (Node.js)

إذا فضلت استخدام سكريبت Node.js:

```bash
cd scripts
npm install
npm run migrate-summaries
```

## ✅ علامات النجاح

- ✅ ظهور تبويب "ملخص الدروس" في واجهة المستخدم
- ✅ عمل الروابط `/summary/:lessonId` بشكل صحيح
- ✅ عرض البيانات (الصور/PDFs) في صفحات الملخص
- ✅ عدم ظهور أخطاء في Console

## ⚠️ في حالة وجود مشاكل

### مشكلة: "table summaries does not exist"
**الحل:** تأكد من تشغيل السكريبت SQL بنجاح

### مشكلة: صفحة فارغة في ملخص الدروس
**الحل:** تحقق من وجود بيانات في جدول summaries:
```sql
SELECT * FROM summaries LIMIT 5;
```

### مشكلة: خطأ في التوجيه
**الحل:** تأكد من إعادة تشغيل التطبيق بعد التحديثات

## 🗑️ تنظيف (اختياري)

بعد التأكد من نجاح النقل لمدة أسبوع:
```sql
DROP TABLE IF EXISTS public.exams;
```

## 📞 الدعم السريع

إذا واجهت مشاكل، تحقق من:
1. **Supabase Logs**: Dashboard > Logs
2. **Browser Console**: F12 > Console
3. **Network Tab**: F12 > Network

---

**💡 نصيحة:** احتفظ بنسخة احتياطية من جدول `exams` لمدة أسبوع قبل حذفه نهائياً.
