'use client'

import { useEffect } from 'react';
import { clearAllCacheAndRefresh } from '@/backend/utils/dataLoader';

// تعريف أنواع البيانات للدوال المساعدة
interface WindowWithCacheHelpers extends Window {
  clearEducationCache?: () => void;
  forceFreshData?: () => void;
  checkCacheStatus?: () => void;
}

// مكون لإدارة التخزين المؤقت وإضافة دوال مساعدة للمطورين
export default function CacheManager() {
  useEffect(() => {
    // إضافة دوال مساعدة إلى window object للاستخدام في وحدة تحكم المتصفح
    if (typeof window !== 'undefined') {
      const windowWithHelpers = window as WindowWithCacheHelpers;

      // دالة مسح التخزين المؤقت
      windowWithHelpers.clearEducationCache = () => {
        console.log('🧹 مسح التخزين المؤقت...');
        
        // مسح التخزين المؤقت من dataLoader
        clearAllCacheAndRefresh();
        
        // مسح localStorage
        const keysToRemove: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('arab-edu') || 
            key.includes('cache_') ||
            key.includes('levels') || 
            key.includes('years') || 
            key.includes('subjects') || 
            key.includes('lessons')
          )) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log(`✅ تم حذف: ${key}`);
        });
        
        console.log(`🗑️ تم حذف ${keysToRemove.length} عنصر من localStorage`);
        console.log('✨ تم مسح التخزين المؤقت بنجاح!');
        console.log('🔄 إعادة تحميل الصفحة...');
        
        // إعادة تحميل الصفحة
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      };
      
      // دالة سريعة لجلب البيانات الجديدة
      windowWithHelpers.forceFreshData = () => {
        console.log('🚀 إجبار جلب البيانات الجديدة...');
        windowWithHelpers.clearEducationCache?.();
      };

      // دالة للتحقق من حالة التخزين المؤقت
      windowWithHelpers.checkCacheStatus = () => {
        console.log('📊 حالة التخزين المؤقت:');
        
        let totalItems = 0;
        const cacheItems: string[] = [];
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('arab-edu') || 
            key.includes('cache_') ||
            key.includes('levels') || 
            key.includes('years') || 
            key.includes('subjects') || 
            key.includes('lessons')
          )) {
            cacheItems.push(key);
            totalItems++;
          }
        }
        
        console.log(`📦 إجمالي العناصر المخزنة: ${totalItems}`);
        if (cacheItems.length > 0) {
          console.log('📋 العناصر المخزنة:');
          cacheItems.forEach(item => console.log(`  - ${item}`));
        } else {
          console.log('🆕 لا توجد عناصر مخزنة - البيانات ستُجلب من Supabase');
        }
      };
      
      // طباعة رسالة ترحيب للمطورين
      console.log('🛠️ أدوات المطور متاحة:');
      console.log('  • clearEducationCache() - مسح التخزين المؤقت وإعادة التحميل');
      console.log('  • forceFreshData() - إجبار جلب البيانات الجديدة');
      console.log('  • checkCacheStatus() - التحقق من حالة التخزين المؤقت');
      console.log('💡 استخدم هذه الدوال إذا لم تظهر التحديثات الجديدة');
    }
  }, []);

  // هذا المكون لا يعرض أي شيء
  return null;
}
