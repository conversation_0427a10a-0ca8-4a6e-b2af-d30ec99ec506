'use client';

import ContentCard from '@/components/ui/content-card';
import { Exercise } from '@/data/types';

interface SummaryProps {
  exercise: Exercise;
  index: number;
  lessonTitle?: string;
}

const SummaryCard = ({
  exercise,
  index,
  lessonTitle
}: SummaryProps) => {
  return (
    <ContentCard
      exerciseImageUrl={exercise.exerciseImageUrl}
      solutionImageUrl={exercise.solutionImageUrl}
      index={index}
      contentType="summary"
      lessonTitle={lessonTitle}
    />
  );
};

export default SummaryCard;
