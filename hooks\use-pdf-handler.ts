'use client';

import { useCallback, useState } from 'react';
import { extractFilenameFromUrl } from '@/utils/arabic-url-utils';

export const usePdfHandler = () => {
  const [isDownloading, setIsDownloading] = useState(false);

  // Check if URL is a PDF file
  const isPdf = useCallback((url?: string): boolean => {
    if (!url) return false;

    const urlLower = url.toLowerCase();

    // Check for common PDF indicators in URLs
    return urlLower.endsWith('.pdf') ||
           urlLower.includes('application/pdf') ||
           urlLower.includes('content-type=pdf') ||
           urlLower.includes('.pdf?') ||
           urlLower.includes('pdf&') ||
           urlLower.includes('type=pdf') ||
           urlLower.includes('output=pdf') ||
           (urlLower.includes('drive.google.com') && urlLower.includes('pdf'));
  }, []);

  // Generate filename from URL or use default
  const getFilenameFromUrl = useCallback((url: string): string => {
    return extractFilenameFromUrl(url, 'مستند');
  }, []);

  // Check if URL is from same origin or allows CORS
  const canUseFetch = useCallback((url: string): boolean => {
    try {
      const urlObj = new URL(url);
      const currentOrigin = window.location.origin;

      // Same origin - always allowed
      if (urlObj.origin === currentOrigin) {
        return true;
      }

      // Known CORS-friendly domains
      const corsFriendlyDomains = [
        'drive.google.com',
        'docs.google.com',
        'githubusercontent.com',
        'raw.githubusercontent.com'
      ];

      // Check for same domain with different subdomain (like d.tolabi.net vs tolabi.net)
      const currentDomain = window.location.hostname.replace(/^[^.]+\./, ''); // Remove subdomain
      const urlDomain = urlObj.hostname.replace(/^[^.]+\./, ''); // Remove subdomain

      // Allow same domain with different subdomains
      if (currentDomain === urlDomain) {
        return true;
      }

      return corsFriendlyDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }, []);

  // Direct download using fetch and blob - Enhanced for better PDF handling
  const downloadFileDirectly = useCallback(async (url: string, filename: string) => {
    try {
      console.log('Attempting direct download for:', url);

      // Enhanced headers for better PDF handling
      const headers: HeadersInit = {
        'Accept': 'application/pdf,application/octet-stream,*/*',
        'Cache-Control': 'no-cache',
      };

      // Add special headers for talamid.ma files
      if (url.includes('talamid.ma')) {
        headers['Origin'] = window.location.origin;
        headers['Referer'] = window.location.href;
      }

      const corsResponse = await fetch(url, {
        method: 'GET',
        headers,
        mode: 'cors',
        credentials: url.includes('talamid.ma') ? 'include' : 'omit',
      }).catch(() => null);

      let response = corsResponse;

      // If CORS failed, try no-cors mode (limited but sometimes works)
      if (!response || !response.ok) {
        console.log('CORS failed, trying no-cors mode...');
        response = await fetch(url, {
          method: 'GET',
          mode: 'no-cors',
        }).catch(() => null);
      }

      if (!response) {
        throw new Error('Both CORS and no-cors fetch failed');
      }

      if (response.type === 'opaque') {
        // no-cors response - we can't read the content
        throw new Error('Opaque response from no-cors mode');
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // Ensure we have a valid blob
      if (!blob || blob.size === 0) {
        throw new Error('Empty or invalid blob received');
      }

      // Create download link with enhanced attributes
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.style.display = 'none';

      // Force download behavior
      link.setAttribute('download', filename);

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up after a short delay
      setTimeout(() => {
        window.URL.revokeObjectURL(downloadUrl);
      }, 100);

      console.log('Direct download completed successfully');
      return true;
    } catch (error) {
      console.log('Direct download failed:', error);
      return false;
    }
  }, []);

  // Enhanced iframe download method
  const downloadUsingIframe = useCallback((url: string, filename?: string) => {
    try {
      console.log('Using enhanced iframe download method for:', url);

      // Create iframe with enhanced attributes for download
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';

      // Set source to trigger download
      iframe.src = url;

      // Add download attributes
      if (filename) {
        iframe.setAttribute('download', filename);
      }

      document.body.appendChild(iframe);

      // Remove iframe after a delay
      setTimeout(() => {
        if (document.body.contains(iframe)) {
          document.body.removeChild(iframe);
        }
      }, 15000); // Increased timeout for better reliability

      return true;
    } catch (error) {
      console.log('Iframe download failed:', error);
      return false;
    }
  }, []);

  // Force download using direct link with download attribute
  const downloadUsingWindowOpen = useCallback((url: string, filename: string) => {
    try {
      console.log('Using direct download link method for:', url);

      // Create a temporary link with download attribute - NO target="_blank"
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      // Remove target="_blank" to prevent opening in new tab
      link.style.display = 'none';

      // Try to trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return true;
    } catch (error) {
      console.log('Direct download link failed:', error);
      return false;
    }
  }, []);

  // Handle PDF download with multiple fallback methods
  const handleDownload = useCallback(async (url: string, customFilename?: string) => {
    if (isDownloading) {
      console.log('Download already in progress, skipping...');
      return;
    }

    setIsDownloading(true);

    try {
      console.log('Starting download for:', url);

      const filename = customFilename || getFilenameFromUrl(url);
      let downloadUrl = url;
      const isExternalUrl = !canUseFetch(url);

      // Enhanced Google Drive handling
      if (url.includes('drive.google.com/file/d/')) {
        const fileIdMatch = url.match(/\/d\/([^\/]+)/);
        if (fileIdMatch && fileIdMatch[1]) {
          const fileId = fileIdMatch[1];
          downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}&confirm=t`;
          console.log('Using enhanced Google Drive direct download link:', downloadUrl);

          // For Google Drive, try immediate download
          try {
            const gdLink = document.createElement('a');
            gdLink.href = downloadUrl;
            gdLink.download = filename;
            gdLink.style.display = 'none';
            gdLink.setAttribute('download', filename);

            document.body.appendChild(gdLink);
            gdLink.click();
            document.body.removeChild(gdLink);

            console.log('Google Drive direct download initiated');
            setIsDownloading(false);
            return;
          } catch (gdError) {
            console.log('Google Drive direct download failed, trying other methods:', gdError);
          }
        }
      }

      // Special handling for talamid.ma subdomain files
      if (url.includes('talamid.ma')) {
        console.log('Detected talamid.ma file, using enhanced download method');

        try {
          // Method for talamid.ma files - direct download with enhanced headers
          const talamidLink = document.createElement('a');
          talamidLink.href = downloadUrl;
          talamidLink.download = filename;
          talamidLink.style.display = 'none';
          talamidLink.setAttribute('download', filename);
          talamidLink.setAttribute('type', 'application/pdf');
          talamidLink.setAttribute('crossorigin', 'anonymous');

          document.body.appendChild(talamidLink);
          talamidLink.click();
          document.body.removeChild(talamidLink);

          console.log('Talamid.ma direct download initiated');
          setIsDownloading(false);
          return;
        } catch (talamidError) {
          console.log('Talamid.ma direct download failed, trying other methods:', talamidError);
        }
      }

      console.log(`URL type: ${isExternalUrl ? 'External' : 'CORS-friendly'}`);

      // Method 1: Try direct download using fetch (for CORS-friendly URLs and talamid.ma)
      if (!isExternalUrl || url.includes('drive.google.com') || url.includes('talamid.ma')) {
        const directDownloadSuccess = await downloadFileDirectly(downloadUrl, filename);
        if (directDownloadSuccess) {
          console.log('Download completed using direct method');
          return;
        }
      }

      // Method 2: Enhanced direct download link (most reliable for PDFs)
      try {
        console.log('Using enhanced direct download link method');
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.style.display = 'none';

        // Force download behavior with multiple attributes
        link.setAttribute('download', filename);
        link.setAttribute('type', 'application/pdf');

        document.body.appendChild(link);

        // Use both click and dispatchEvent for better compatibility
        link.click();

        // Alternative trigger method
        const clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: false
        });
        link.dispatchEvent(clickEvent);

        document.body.removeChild(link);

        console.log('Enhanced direct download link completed');
        return;
      } catch (enhancedError) {
        console.log('Enhanced direct download failed:', enhancedError);
      }

      // Method 3: Try window.open with download intent (fallback)
      const windowOpenSuccess = downloadUsingWindowOpen(downloadUrl, filename);
      if (windowOpenSuccess) {
        console.log('Download initiated using window.open method');
        return;
      }

      // Method 4: Try iframe download (works for some external sites)
      const iframeDownloadSuccess = downloadUsingIframe(downloadUrl, filename);
      if (iframeDownloadSuccess) {
        console.log('Download initiated using iframe method');
        return;
      }

      // Method 5: Simple download link with download attribute
      try {
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Download initiated using simple download link method');
        return;
      } catch (linkError) {
        console.log('Simple download link method failed:', linkError);
      }

      // Method 6: Force download by creating blob URL (for external files)
      try {
        console.log('Attempting blob URL download method');

        // Create a temporary iframe to load the file
        const tempIframe = document.createElement('iframe');
        tempIframe.style.display = 'none';
        tempIframe.src = downloadUrl;
        document.body.appendChild(tempIframe);

        // Remove after delay
        setTimeout(() => {
          if (document.body.contains(tempIframe)) {
            document.body.removeChild(tempIframe);
          }
        }, 5000);

        console.log('Blob URL download method initiated');
        return;
      } catch (blobError) {
        console.log('Blob URL download method failed:', blobError);
      }

    } catch (error) {
      console.error('All download methods failed:', error);

      // Final fallback - open in new tab (original behavior)
      console.log('Using final fallback - opening in new tab');
      window.open(url, '_blank');
    } finally {
      setIsDownloading(false);
    }
  }, [isDownloading, getFilenameFromUrl, canUseFetch, downloadFileDirectly, downloadUsingWindowOpen, downloadUsingIframe]);

  return {
    isPdf,
    handleDownload,
    isDownloading
  };
};
