'use client'

import { useState, useEffect, useMemo } from "react";
import DesktopHeader from "./DesktopHeader";
import MobileHeader from "./MobileHeader";
import { useIsMobile } from "@/hooks/use-mobile";
import { getLevels, getYearsForLevel } from "@/data/educationData";
import type { Level, Year } from "@/data/types";

// Custom hook for header data management
const useHeaderData = () => {
  const [levels, setLevels] = useState<Level[]>([]);
  const [yearsByLevel, setYearsByLevel] = useState<Record<string, Year[]>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const levelsData = await getLevels();
        setLevels(levelsData);

        // Load years for each level
        const yearsData: Record<string, Year[]> = {};
        await Promise.all(
          levelsData.map(async (level) => {
            const years = await getYearsForLevel(level.id);
            yearsData[level.id] = years;
          })
        );
        setYearsByLevel(yearsData);
      } catch (error) {
        console.error('Error loading header data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return { levels, yearsByLevel, loading };
};

const Header = () => {
  const { levels, yearsByLevel, loading } = useHeaderData();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  // Memoize filtered levels for better performance
  const { primaryLevels, middleLevels, trunkCommonLevels, firstBacLevels, secondBacLevels } = useMemo(() => ({
    primaryLevels: levels.filter(level => level.name.includes('ابتدائي')),
    middleLevels: levels.filter(level => level.name.includes('الإعدادي')),
    trunkCommonLevels: levels.filter(level => level.name.includes('جذع مشترك')),
    firstBacLevels: levels.filter(level => level.name.includes('الأولى باك')),
    secondBacLevels: levels.filter(level => level.name.includes('الثانية باك'))
  }), [levels]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return isMobile ? (
    <MobileHeader
      primaryLevels={primaryLevels}
      middleLevels={middleLevels}
      trunkCommonLevels={trunkCommonLevels}
      firstBacLevels={firstBacLevels}
      secondBacLevels={secondBacLevels}
      yearsByLevel={yearsByLevel}
      loading={loading}
      mobileMenuOpen={mobileMenuOpen}
      toggleMobileMenu={toggleMobileMenu}
    />
  ) : (
    <DesktopHeader
      primaryLevels={primaryLevels}
      middleLevels={middleLevels}
      trunkCommonLevels={trunkCommonLevels}
      firstBacLevels={firstBacLevels}
      secondBacLevels={secondBacLevels}
      yearsByLevel={yearsByLevel}
      loading={loading}
    />
  );
};

export default Header;
