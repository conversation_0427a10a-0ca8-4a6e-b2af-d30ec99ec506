#!/bin/bash

# سكريبت اختبار الأداء السريع
echo "🚀 بدء اختبار الأداء المحسن..."

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة للطباعة الملونة
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# التحقق من Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت"
    exit 1
fi

print_status "Node.js متاح"

# التحقق من npm
if ! command -v npm &> /dev/null; then
    print_error "npm غير متاح"
    exit 1
fi

print_status "npm متاح"

# تنظيف المشروع
print_info "تنظيف المشروع..."
rm -rf .next
rm -rf node_modules/.cache
print_status "تم تنظيف المشروع"

# بناء المشروع
print_info "بناء المشروع مع التحسينات الجديدة..."
if npm run build; then
    print_status "تم بناء المشروع بنجاح"
else
    print_error "فشل في بناء المشروع"
    exit 1
fi

# تحليل الأداء
print_info "تشغيل تحليل الأداء..."
if npm run analyze; then
    print_status "تم تحليل الأداء بنجاح"
else
    print_warning "تحذير: فشل في تحليل الأداء"
fi

# تشغيل المشروع في الخلفية
print_info "تشغيل المشروع..."
npm run start &
SERVER_PID=$!

# انتظار تشغيل الخادم
print_info "انتظار تشغيل الخادم..."
sleep 10

# التحقق من تشغيل الخادم
if curl -s http://localhost:3000 > /dev/null; then
    print_status "الخادم يعمل على http://localhost:3000"
else
    print_error "فشل في تشغيل الخادم"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# اختبار الصفحات الرئيسية
print_info "اختبار الصفحات الرئيسية..."

pages=("/" "/levels")
for page in "${pages[@]}"; do
    if curl -s "http://localhost:3000$page" > /dev/null; then
        print_status "صفحة $page تعمل"
    else
        print_warning "مشكلة في صفحة $page"
    fi
done

# اختبار Lighthouse (إذا كان متاحاً)
if command -v lighthouse &> /dev/null; then
    print_info "تشغيل اختبار Lighthouse..."
    lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html --chrome-flags="--headless" --quiet
    if [ $? -eq 0 ]; then
        print_status "تم إنشاء تقرير Lighthouse: lighthouse-report.html"
    else
        print_warning "فشل في تشغيل Lighthouse"
    fi
else
    print_warning "Lighthouse غير مثبت. لتثبيته: npm install -g lighthouse"
fi

# عرض النتائج
echo ""
echo "🎉 انتهى اختبار الأداء!"
echo ""
echo "📊 النتائج:"
echo "✅ المشروع يعمل على: http://localhost:3000"
echo "✅ تم تطبيق جميع التحسينات"
echo "✅ الصفحات الرئيسية تعمل"

if [ -f "lighthouse-report.html" ]; then
    echo "✅ تقرير Lighthouse متاح: lighthouse-report.html"
fi

if [ -f "performance-report.json" ]; then
    echo "✅ تقرير الأداء متاح: performance-report.json"
fi

echo ""
echo "🔧 للاختبار اليدوي:"
echo "1. افتح http://localhost:3000 في المتصفح"
echo "2. افتح Developer Tools (F12)"
echo "3. راقب Network tab"
echo "4. تحقق من Console للرسائل"
echo "5. استخدم Performance tab للتحليل"

echo ""
echo "⏹️  لإيقاف الخادم: kill $SERVER_PID"
echo "📝 أو اضغط Ctrl+C"

# انتظار إدخال المستخدم لإيقاف الخادم
echo ""
read -p "اضغط Enter لإيقاف الخادم..." -r

# إيقاف الخادم
print_info "إيقاف الخادم..."
kill $SERVER_PID 2>/dev/null
print_status "تم إيقاف الخادم"

echo ""
echo "🎯 التوصيات التالية:"
echo "1. راجع lighthouse-report.html للتفاصيل"
echo "2. راجع performance-report.json للإحصائيات"
echo "3. اختبر على أجهزة مختلفة"
echo "4. اختبر على شبكات بطيئة"
echo "5. راقب الأداء بانتظام"

echo ""
echo "🚀 مبروك! موقعك محسن للأداء العالي!"
