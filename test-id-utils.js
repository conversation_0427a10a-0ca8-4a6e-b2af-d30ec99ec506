/**
 * Test file for ID utilities with spaces
 * ملف اختبار لدوال معالجة المعرفات التي تحتوي على مسافات
 */

// Import the functions (for Node.js testing)
// استيراد الدوال (للاختبار في Node.js)
const {
  encodeIdForUrl,
  decodeIdFromUrl,
  sanitizeIdForDatabase,
  isValidId,
  buildUrlWithId,
  extractIdFromPathname,
  createSafeFilename,
  compareIds,
  batchEncodeIds,
  batchDecodeIds
} = require('./utils/id-utils.ts');

console.log('🧪 اختبار دوال معالجة المعرفات مع المسافات');
console.log('='.repeat(50));

// Test cases with spaces and special characters
// حالات اختبار مع مسافات ورموز خاصة
const testIds = [
  'السنة الأولى ابتدائي',
  'الرياضيات - الصف السابع',
  'درس الجمع والطرح',
  'امتحان نهاية الفصل الأول',
  'تمرين رقم 1 - الهندسة',
  'ملخص الوحدة الثانية',
  'فرض منزلي - الأسبوع الثالث',
  'normal_id_without_spaces',
  'id-with-dashes',
  'id_with_underscores',
  'مادة العلوم الطبيعية',
  'اللغة العربية والأدب'
];

console.log('\n1️⃣ اختبار ترميز وفك ترميز المعرفات:');
console.log('-'.repeat(40));

testIds.forEach((id, index) => {
  console.log(`\nالاختبار ${index + 1}: "${id}"`);
  
  // Test encoding
  const encoded = encodeIdForUrl(id);
  console.log(`  مُرمز: ${encoded}`);
  
  // Test decoding
  const decoded = decodeIdFromUrl(encoded);
  console.log(`  مفكوك: ${decoded}`);
  
  // Test if original matches decoded
  const matches = compareIds(id, decoded);
  console.log(`  متطابق: ${matches ? '✅' : '❌'}`);
  
  // Test database sanitization
  const sanitized = sanitizeIdForDatabase(id);
  console.log(`  منظف للقاعدة: "${sanitized}"`);
});

console.log('\n2️⃣ اختبار بناء الروابط:');
console.log('-'.repeat(40));

testIds.slice(0, 5).forEach((id, index) => {
  const url = buildUrlWithId('/lesson/', id, { returnTo: '/subject/math', tab: 'exercises' });
  console.log(`\nرابط ${index + 1}: ${url}`);
});

console.log('\n3️⃣ اختبار استخراج المعرفات من المسارات:');
console.log('-'.repeat(40));

const testPaths = [
  '/lesson/السنة%20الأولى%20ابتدائي',
  '/subject/الرياضيات%20-%20الصف%20السابع',
  '/exam/امتحان%20نهاية%20الفصل%20الأول',
  '/homework/فرض%20منزلي%20-%20الأسبوع%20الثالث'
];

testPaths.forEach((path, index) => {
  const extractedId = extractIdFromPathname(path);
  console.log(`\nمسار ${index + 1}: ${path}`);
  console.log(`  المعرف المستخرج: "${extractedId}"`);
});

console.log('\n4️⃣ اختبار إنشاء أسماء ملفات آمنة:');
console.log('-'.repeat(40));

testIds.slice(0, 5).forEach((id, index) => {
  const safeFilename = createSafeFilename(id, '.pdf');
  console.log(`\nمعرف ${index + 1}: "${id}"`);
  console.log(`  اسم ملف آمن: ${safeFilename}`);
});

console.log('\n5️⃣ اختبار التحقق من صحة المعرفات:');
console.log('-'.repeat(40));

const validationTests = [
  'معرف صحيح',
  '',
  null,
  undefined,
  '   ',
  'معرف آخر صحيح'
];

validationTests.forEach((id, index) => {
  const isValid = isValidId(id);
  console.log(`\nاختبار ${index + 1}: ${JSON.stringify(id)} -> ${isValid ? '✅ صحيح' : '❌ غير صحيح'}`);
});

console.log('\n6️⃣ اختبار الترميز المتعدد:');
console.log('-'.repeat(40));

const batchIds = testIds.slice(0, 4);
console.log('\nالمعرفات الأصلية:', batchIds);

const encodedBatch = batchEncodeIds(batchIds);
console.log('\nمُرمزة:', encodedBatch);

const decodedBatch = batchDecodeIds(encodedBatch);
console.log('\nمفكوكة:', decodedBatch);

// Check if all match
const allMatch = batchIds.every((id, index) => compareIds(id, decodedBatch[index]));
console.log(`\nجميع المعرفات متطابقة: ${allMatch ? '✅' : '❌'}`);

console.log('\n7️⃣ اختبار حالات خاصة:');
console.log('-'.repeat(40));

// Test with very long ID
const longId = 'هذا معرف طويل جداً يحتوي على مسافات كثيرة ورموز خاصة مثل @ # $ % ^ & * ( ) - + = { } [ ] | \\ : ; " \' < > , . ? / ~ `';
console.log('\nمعرف طويل:');
console.log(`  الأصلي: ${longId.substring(0, 50)}...`);
console.log(`  مُرمز: ${encodeIdForUrl(longId).substring(0, 50)}...`);
console.log(`  اسم ملف آمن: ${createSafeFilename(longId, '.pdf')}`);

// Test with Arabic numbers
const arabicNumberId = 'الصف ١٢ - الوحدة ٣';
console.log('\nمعرف بأرقام عربية:');
console.log(`  الأصلي: ${arabicNumberId}`);
console.log(`  مُرمز: ${encodeIdForUrl(arabicNumberId)}`);
console.log(`  مفكوك: ${decodeIdFromUrl(encodeIdForUrl(arabicNumberId))}`);

console.log('\n✅ انتهى الاختبار بنجاح!');
console.log('='.repeat(50));

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testIds,
    testPaths,
    validationTests
  };
}
