'use client'

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { ArrowLeft, Home } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import HomeworkList from '@/components/homework/HomeworkList';
import { useLesson, useSubject, useYear, useLevel } from '@/hooks/use-education-data';
import { useViewerMode } from '@/hooks/use-viewer-mode';

interface HomeworkClientProps {
  lessonId: string;
}

export default function HomeworkClient({ lessonId }: HomeworkClientProps) {
  const { isViewerMode } = useViewerMode();
  const searchParams = useSearchParams();

  // الحصول على رابط العودة من معاملات URL
  const returnTo = searchParams.get('returnTo');

  // استخدام React Query hooks للحصول على البيانات
  const { data: lesson, isLoading: lessonLoading, isError: lessonError } = useLesson(lessonId);
  const { data: subject, isLoading: subjectLoading } = useSubject(lesson?.subjectId || '');
  const { data: year, isLoading: yearLoading } = useYear(subject?.yearId || '');
  const { data: level, isLoading: levelLoading } = useLevel(year?.levelId || '');

  const loading = lessonLoading || subjectLoading || yearLoading || levelLoading;
  const error = lessonError;

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg">جاري تحميل البيانات...</p>
          </div>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  if (error || !lesson) {
    return (
      <div className="min-h-screen flex flex-col">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-destructive mb-4">خطأ</h1>
            <p className="text-lg mb-4">{'لم يتم العثور على الواجب'}</p>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة إلى المستويات
            </Link>
          </div>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {!isViewerMode && <Header />}

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">{lesson.title}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            {lesson.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground">
            <Link href="/levels" className="hover:text-primary transition-colors">
              المستويات
            </Link>
            <span>/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors">
                  {year.name}
                </Link>
                <span>/</span>
              </>
            )}
            {subject && (
              <>
                <Link href={returnTo || `/subject/${subject.id}`} className="hover:text-primary transition-colors">
                  {subject.name}
                </Link>
                <span>/</span>
              </>
            )}
            <span className="text-foreground">{lesson.title}</span>
          </nav>
        </div>

        {/* Homework Exercises */}
        {lesson.exercises.length > 0 ? (
          <HomeworkList
            exercises={lesson.exercises}
            lessonTitle={lesson.title}
            subjectName={subject?.name}
            yearName={year?.name}
            levelName={level?.name}
          />
        ) : (
          <div className="text-center py-12">
            <Home className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">لا توجد فروض</h3>
            <p className="text-muted-foreground mb-6">
              لم يتم إضافة فروض لهذا الدرس بعد
            </p>
            {subject && (
              <Link
                href={returnTo || `/subject/${subject.id}`}
                className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة إلى المادة
              </Link>
            )}
          </div>
        )}
      </div>

      {!isViewerMode && (
        <div className="mt-auto">
          <Footer />
        </div>
      )}
    </div>
  );
}
