import { NextResponse } from 'next/server'
import { supabase } from '@/integrations/supabase/client'

// API route للتحقق من صحة الموقع وقاعدة البيانات
export async function GET() {
  try {
    const startTime = Date.now()
    
    // التحقق من اتصال قاعدة البيانات
    const { error: dbError } = await supabase
      .from('levels')
      .select('count')
      .limit(1)
    
    const dbStatus = dbError ? 'error' : 'healthy'
    const responseTime = Date.now() - startTime
    
    // معلومات الصحة العامة
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbStatus,
          error: dbError?.message || null,
          responseTime: `${responseTime}ms`
        },
        application: {
          status: 'healthy',
          version: process.env.npm_package_version || '1.0.0',
          environment: process.env.NODE_ENV || 'development'
        }
      },
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    }
    
    // إذا كانت قاعدة البيانات لا تعمل، تغيير الحالة العامة
    if (dbStatus === 'error') {
      healthData.status = 'degraded'
    }
    
    const statusCode = healthData.status === 'healthy' ? 200 : 503
    
    return NextResponse.json(healthData, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch (error) {
    console.error('خطأ في فحص صحة الموقع:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      services: {
        database: { status: 'unknown' },
        application: { status: 'error' }
      }
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

// دعم HEAD request للفحص السريع
export async function HEAD() {
  try {
    // فحص سريع بدون تفاصيل
    const { error } = await supabase
      .from('levels')
      .select('count')
      .limit(1)
    
    const statusCode = error ? 503 : 200
    
    return new Response(null, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch {
    return new Response(null, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}
