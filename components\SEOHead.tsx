'use client'

import { useEffect } from 'react'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string[]
  canonicalUrl?: string
  ogImage?: string
  structuredData?: object
  noindex?: boolean
  nofollow?: boolean
}

export function SEOHead({
  title,
  description,
  keywords = [],
  canonicalUrl,
  ogImage,
  structuredData,
  noindex = false,
  nofollow = false
}: SEOHeadProps) {
  useEffect(() => {
    // Update document title
    if (title) {
      document.title = title
    }

    // Update meta description
    if (description) {
      let metaDescription = document.querySelector('meta[name="description"]')
      if (!metaDescription) {
        metaDescription = document.createElement('meta')
        metaDescription.setAttribute('name', 'description')
        document.head.appendChild(metaDescription)
      }
      metaDescription.setAttribute('content', description)
    }

    // Update meta keywords
    if (keywords.length > 0) {
      let metaKeywords = document.querySelector('meta[name="keywords"]')
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta')
        metaKeywords.setAttribute('name', 'keywords')
        document.head.appendChild(metaKeywords)
      }
      metaKeywords.setAttribute('content', keywords.join(', '))
    }

    // Update canonical URL
    if (canonicalUrl) {
      let linkCanonical = document.querySelector('link[rel="canonical"]')
      if (!linkCanonical) {
        linkCanonical = document.createElement('link')
        linkCanonical.setAttribute('rel', 'canonical')
        document.head.appendChild(linkCanonical)
      }
      linkCanonical.setAttribute('href', canonicalUrl)
    }

    // Update Open Graph tags
    if (title) {
      updateMetaProperty('og:title', title)
    }
    if (description) {
      updateMetaProperty('og:description', description)
    }
    if (ogImage) {
      updateMetaProperty('og:image', ogImage)
    }
    if (canonicalUrl) {
      updateMetaProperty('og:url', canonicalUrl)
    }

    // Update Twitter Card tags
    if (title) {
      updateMetaName('twitter:title', title)
    }
    if (description) {
      updateMetaName('twitter:description', description)
    }
    if (ogImage) {
      updateMetaName('twitter:image', ogImage)
    }

    // Update robots meta tag
    const robotsContent = []
    if (noindex) robotsContent.push('noindex')
    else robotsContent.push('index')
    
    if (nofollow) robotsContent.push('nofollow')
    else robotsContent.push('follow')

    updateMetaName('robots', robotsContent.join(', '))

    // Add structured data
    if (structuredData) {
      let structuredDataScript = document.querySelector('script[type="application/ld+json"][data-seo-head]')
      if (!structuredDataScript) {
        structuredDataScript = document.createElement('script')
        structuredDataScript.setAttribute('type', 'application/ld+json')
        structuredDataScript.setAttribute('data-seo-head', 'true')
        document.head.appendChild(structuredDataScript)
      }
      structuredDataScript.textContent = JSON.stringify(structuredData)
    }

  }, [title, description, keywords, canonicalUrl, ogImage, structuredData, noindex, nofollow])

  return null
}

function updateMetaProperty(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`)
  if (!meta) {
    meta = document.createElement('meta')
    meta.setAttribute('property', property)
    document.head.appendChild(meta)
  }
  meta.setAttribute('content', content)
}

function updateMetaName(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`)
  if (!meta) {
    meta = document.createElement('meta')
    meta.setAttribute('name', name)
    document.head.appendChild(meta)
  }
  meta.setAttribute('content', content)
}

// Hook for tracking page views with Google Analytics
export function usePageView(url: string) {
  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_title: document.title,
        page_location: url,
        send_page_view: true
      })
    }
  }, [url])
}

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void
  }
}
