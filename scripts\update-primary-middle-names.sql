-- سكريبت تحديث أسماء المراحل الابتدائية والإعدادية في Supabase
-- تحديث الأسماء لتتماشى مع النظام التعليمي المغربي

-- 1. تحديث أسماء المراحل الابتدائية
UPDATE levels
SET name = 'الابتدائي',
    description = 'المرحلة الابتدائية في النظام التعليمي المغربي من السنة الأولى إلى السادسة'
WHERE id = 'primary';

UPDATE levels
SET name = 'الإعدادي',
    description = 'المرحلة الإعدادية في النظام التعليمي المغربي من السنة الأولى إلى الثالثة'
WHERE id = 'middle';

-- 2. تحديث أسماء السنوات الابتدائية
UPDATE years SET name = 'الأول ابتدائي' WHERE id = 'grade1';
UPDATE years SET name = 'الثاني ابتدائي' WHERE id = 'grade2';
UPDATE years SET name = 'الثالث ابتدائي' WHERE id = 'grade3';
UPDATE years SET name = 'الرابع ابتدائي' WHERE id = 'grade4';
UPDATE years SET name = 'الخامس ابتدائي' WHERE id = 'grade5';
UPDATE years SET name = 'السادس ابتدائي' WHERE id = 'grade6';

-- 3. تحديث أسماء السنوات الإعدادية
UPDATE years SET name = 'السنة الأولى إعدادي' WHERE id = 'grade7';
UPDATE years SET name = 'السنة الثانية إعدادي' WHERE id = 'grade8';
UPDATE years SET name = 'السنة الثالثة إعدادي' WHERE id = 'grade9';

-- 4. التحقق من النتائج
SELECT 'المستويات المحدثة:' as info;
SELECT id, name, description FROM levels WHERE id IN ('primary', 'middle');

SELECT 'السنوات الابتدائية المحدثة:' as info;
SELECT id, name, level_id FROM years WHERE level_id = 'primary' ORDER BY id;

SELECT 'السنوات الإعدادية المحدثة:' as info;
SELECT id, name, level_id FROM years WHERE level_id = 'middle' ORDER BY id;

-- 5. التحقق النهائي من البنية
SELECT 'التحقق النهائي من الأسماء الجديدة:' as final_check;

-- عرض المستويات المحدثة
SELECT 'المستويات:' as section, id, name, description
FROM levels WHERE id IN ('primary', 'middle') ORDER BY display_order;

-- عرض جميع السنوات المحدثة
SELECT 'جميع السنوات:' as section, id, name, level_id
FROM years WHERE level_id IN ('primary', 'middle') ORDER BY level_id, id;

-- إحصائيات التحديث
SELECT
    'إحصائيات التحديث' as info,
    (SELECT COUNT(*) FROM levels WHERE id IN ('primary', 'middle')) as updated_levels,
    (SELECT COUNT(*) FROM years WHERE level_id IN ('primary', 'middle')) as updated_years;

-- 6. إضافة المواد الدراسية للمرحلة الابتدائية حسب النظام المغربي

-- مواد الأول ابتدائي (6 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade1_arabic', 'اللغة العربية', '📚', 'grade1', ARRAY[]::text[]),
('grade1_french', 'اللغة الفرنسية', '🇫🇷', 'grade1', ARRAY[]::text[]),
('grade1_math', 'الرياضيات', '📐', 'grade1', ARRAY[]::text[]),
('grade1_activity', 'النشاط العلمي', '🔬', 'grade1', ARRAY[]::text[]),
('grade1_islamic', 'التربية الإسلامية', '☪️', 'grade1', ARRAY[]::text[]),
('grade1_arts', 'التربية التشكيلية', '🎨', 'grade1', ARRAY[]::text[]);

-- مواد الثاني ابتدائي (6 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade2_arabic', 'اللغة العربية', '📚', 'grade2', ARRAY[]::text[]),
('grade2_french', 'اللغة الفرنسية', '🇫🇷', 'grade2', ARRAY[]::text[]),
('grade2_math', 'الرياضيات', '📐', 'grade2', ARRAY[]::text[]),
('grade2_activity', 'النشاط العلمي', '🔬', 'grade2', ARRAY[]::text[]),
('grade2_islamic', 'التربية الإسلامية', '☪️', 'grade2', ARRAY[]::text[]),
('grade2_arts', 'التربية التشكيلية', '🎨', 'grade2', ARRAY[]::text[]);

-- مواد الثالث ابتدائي (6 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade3_arabic', 'اللغة العربية', '📚', 'grade3', ARRAY[]::text[]),
('grade3_french', 'اللغة الفرنسية', '🇫🇷', 'grade3', ARRAY[]::text[]),
('grade3_math', 'الرياضيات', '📐', 'grade3', ARRAY[]::text[]),
('grade3_activity', 'النشاط العلمي', '🔬', 'grade3', ARRAY[]::text[]),
('grade3_islamic', 'التربية الإسلامية', '☪️', 'grade3', ARRAY[]::text[]),
('grade3_arts', 'التربية التشكيلية', '🎨', 'grade3', ARRAY[]::text[]);

-- مواد الرابع ابتدائي (7 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade4_arabic', 'اللغة العربية', '📚', 'grade4', ARRAY[]::text[]),
('grade4_french', 'اللغة الفرنسية', '🇫🇷', 'grade4', ARRAY[]::text[]),
('grade4_math', 'الرياضيات', '📐', 'grade4', ARRAY[]::text[]),
('grade4_activity', 'النشاط العلمي', '🔬', 'grade4', ARRAY[]::text[]),
('grade4_islamic', 'التربية الإسلامية', '☪️', 'grade4', ARRAY[]::text[]),
('grade4_arts', 'التربية التشكيلية', '🎨', 'grade4', ARRAY[]::text[]),
('grade4_history', 'التاريخ والجغرافيا', '🌍', 'grade4', ARRAY[]::text[]);

-- مواد الخامس ابتدائي (7 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade5_arabic', 'اللغة العربية', '📚', 'grade5', ARRAY[]::text[]),
('grade5_french', 'اللغة الفرنسية', '🇫🇷', 'grade5', ARRAY[]::text[]),
('grade5_math', 'الرياضيات', '📐', 'grade5', ARRAY[]::text[]),
('grade5_activity', 'النشاط العلمي', '🔬', 'grade5', ARRAY[]::text[]),
('grade5_islamic', 'التربية الإسلامية', '☪️', 'grade5', ARRAY[]::text[]),
('grade5_arts', 'التربية التشكيلية', '🎨', 'grade5', ARRAY[]::text[]),
('grade5_history', 'التاريخ والجغرافيا', '🌍', 'grade5', ARRAY[]::text[]);

-- مواد السادس ابتدائي (7 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade6_arabic', 'اللغة العربية', '📚', 'grade6', ARRAY[]::text[]),
('grade6_french', 'اللغة الفرنسية', '🇫🇷', 'grade6', ARRAY[]::text[]),
('grade6_math', 'الرياضيات', '📐', 'grade6', ARRAY[]::text[]),
('grade6_activity', 'النشاط العلمي', '🔬', 'grade6', ARRAY[]::text[]),
('grade6_islamic', 'التربية الإسلامية', '☪️', 'grade6', ARRAY[]::text[]),
('grade6_arts', 'التربية التشكيلية', '🎨', 'grade6', ARRAY[]::text[]),
('grade6_history', 'التاريخ والجغرافيا', '🌍', 'grade6', ARRAY[]::text[]);

-- 7. إضافة المواد الدراسية للمرحلة الإعدادية حسب النظام المغربي

-- مواد السنة الأولى إعدادي (9 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade7_arabic', 'اللغة العربية', '📚', 'grade7', ARRAY[]::text[]),
('grade7_french', 'اللغة الفرنسية', '🇫🇷', 'grade7', ARRAY[]::text[]),
('grade7_english', 'اللغة الإنجليزية', '🇺🇸', 'grade7', ARRAY[]::text[]),
('grade7_math', 'الرياضيات', '📐', 'grade7', ARRAY[]::text[]),
('grade7_physics', 'الفيزياء والكيمياء', '⚛️', 'grade7', ARRAY[]::text[]),
('grade7_biology', 'علوم الحياة والأرض', '🌱', 'grade7', ARRAY[]::text[]),
('grade7_history', 'التاريخ والجغرافيا', '🌍', 'grade7', ARRAY[]::text[]),
('grade7_islamic', 'التربية الإسلامية', '☪️', 'grade7', ARRAY[]::text[]),
('grade7_arts', 'التربية التشكيلية', '🎨', 'grade7', ARRAY[]::text[]);

-- مواد السنة الثانية إعدادي (10 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade8_arabic', 'اللغة العربية', '📚', 'grade8', ARRAY[]::text[]),
('grade8_french', 'اللغة الفرنسية', '🇫🇷', 'grade8', ARRAY[]::text[]),
('grade8_english', 'اللغة الإنجليزية', '🇺🇸', 'grade8', ARRAY[]::text[]),
('grade8_math', 'الرياضيات', '📐', 'grade8', ARRAY[]::text[]),
('grade8_physics', 'الفيزياء والكيمياء', '⚛️', 'grade8', ARRAY[]::text[]),
('grade8_biology', 'علوم الحياة والأرض', '🌱', 'grade8', ARRAY[]::text[]),
('grade8_history', 'التاريخ والجغرافيا', '🌍', 'grade8', ARRAY[]::text[]),
('grade8_islamic', 'التربية الإسلامية', '☪️', 'grade8', ARRAY[]::text[]),
('grade8_arts', 'التربية التشكيلية', '🎨', 'grade8', ARRAY[]::text[]),
('grade8_technology', 'التكنولوجيا الصناعية', '⚙️', 'grade8', ARRAY[]::text[]);

-- مواد السنة الثالثة إعدادي (10 مواد)
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('grade9_arabic', 'اللغة العربية', '📚', 'grade9', ARRAY[]::text[]),
('grade9_french', 'اللغة الفرنسية', '🇫🇷', 'grade9', ARRAY[]::text[]),
('grade9_english', 'اللغة الإنجليزية', '🇺🇸', 'grade9', ARRAY[]::text[]),
('grade9_math', 'الرياضيات', '📐', 'grade9', ARRAY[]::text[]),
('grade9_physics', 'الفيزياء والكيمياء', '⚛️', 'grade9', ARRAY[]::text[]),
('grade9_biology', 'علوم الحياة والأرض', '🌱', 'grade9', ARRAY[]::text[]),
('grade9_history', 'التاريخ والجغرافيا', '🌍', 'grade9', ARRAY[]::text[]),
('grade9_islamic', 'التربية الإسلامية', '☪️', 'grade9', ARRAY[]::text[]),
('grade9_arts', 'التربية التشكيلية', '🎨', 'grade9', ARRAY[]::text[]),
('grade9_technology', 'التكنولوجيا الصناعية', '⚙️', 'grade9', ARRAY[]::text[]);

-- 8. تحديث مصفوفة subjects في جدول years للمرحلة الابتدائية
UPDATE years SET subjects = ARRAY[
    'grade1_arabic', 'grade1_french', 'grade1_math', 'grade1_activity', 'grade1_islamic', 'grade1_arts'
] WHERE id = 'grade1';

UPDATE years SET subjects = ARRAY[
    'grade2_arabic', 'grade2_french', 'grade2_math', 'grade2_activity', 'grade2_islamic', 'grade2_arts'
] WHERE id = 'grade2';

UPDATE years SET subjects = ARRAY[
    'grade3_arabic', 'grade3_french', 'grade3_math', 'grade3_activity', 'grade3_islamic', 'grade3_arts'
] WHERE id = 'grade3';

UPDATE years SET subjects = ARRAY[
    'grade4_arabic', 'grade4_french', 'grade4_math', 'grade4_activity', 'grade4_islamic', 'grade4_arts', 'grade4_history'
] WHERE id = 'grade4';

UPDATE years SET subjects = ARRAY[
    'grade5_arabic', 'grade5_french', 'grade5_math', 'grade5_activity', 'grade5_islamic', 'grade5_arts', 'grade5_history'
] WHERE id = 'grade5';

UPDATE years SET subjects = ARRAY[
    'grade6_arabic', 'grade6_french', 'grade6_math', 'grade6_activity', 'grade6_islamic', 'grade6_arts', 'grade6_history'
] WHERE id = 'grade6';

-- 9. تحديث مصفوفة subjects في جدول years للمرحلة الإعدادية
UPDATE years SET subjects = ARRAY[
    'grade7_arabic', 'grade7_french', 'grade7_english', 'grade7_math', 'grade7_physics', 'grade7_biology', 'grade7_history', 'grade7_islamic', 'grade7_arts'
] WHERE id = 'grade7';

UPDATE years SET subjects = ARRAY[
    'grade8_arabic', 'grade8_french', 'grade8_english', 'grade8_math', 'grade8_physics', 'grade8_biology', 'grade8_history', 'grade8_islamic', 'grade8_arts', 'grade8_technology'
] WHERE id = 'grade8';

UPDATE years SET subjects = ARRAY[
    'grade9_arabic', 'grade9_french', 'grade9_english', 'grade9_math', 'grade9_physics', 'grade9_biology', 'grade9_history', 'grade9_islamic', 'grade9_arts', 'grade9_technology'
] WHERE id = 'grade9';

-- 10. عرض النتائج النهائية للمواد المضافة
SELECT 'المواد الابتدائية المضافة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6') ORDER BY year_id, name;

SELECT 'المواد الإعدادية المضافة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN ('grade7', 'grade8', 'grade9') ORDER BY year_id, name;

-- 11. إحصائيات المواد المضافة
SELECT
    'إحصائيات المواد الجديدة' as info,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6')) as primary_subjects,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN ('grade7', 'grade8', 'grade9')) as middle_subjects,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6', 'grade7', 'grade8', 'grade9')) as total_subjects;

-- 12. التحقق النهائي من البنية الكاملة
SELECT 'التحقق النهائي من البنية الكاملة:' as final_verification;

-- عرض المستويات المحدثة
SELECT 'المستويات المحدثة:' as section, id, name, description
FROM levels WHERE id IN ('primary', 'middle') ORDER BY id;

-- عرض السنوات مع عدد المواد
SELECT
    'السنوات مع عدد المواد:' as section,
    y.id as year_id,
    y.name as year_name,
    y.level_id,
    array_length(y.subjects, 1) as subjects_count
FROM years y
WHERE y.level_id IN ('primary', 'middle')
ORDER BY y.level_id, y.id;

-- عرض تفصيل المواد لكل سنة
SELECT
    'تفصيل المواد لكل سنة:' as section,
    s.year_id,
    COUNT(*) as subjects_count,
    string_agg(s.name, ', ' ORDER BY s.name) as subjects_list
FROM subjects s
WHERE s.year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6', 'grade7', 'grade8', 'grade9')
GROUP BY s.year_id
ORDER BY s.year_id;

-- 13. ملخص شامل للنظام التعليمي المغربي
SELECT 'ملخص النظام التعليمي المغربي المحدث:' as summary;

SELECT
    'الإحصائيات الشاملة' as category,
    (SELECT COUNT(*) FROM levels WHERE id IN ('primary', 'middle')) as levels_count,
    (SELECT COUNT(*) FROM years WHERE level_id IN ('primary', 'middle')) as years_count,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6', 'grade7', 'grade8', 'grade9')) as subjects_count;

-- عرض توزيع المواد حسب المرحلة
SELECT
    'توزيع المواد حسب المرحلة' as distribution,
    CASE
        WHEN s.year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6') THEN 'الابتدائي'
        WHEN s.year_id IN ('grade7', 'grade8', 'grade9') THEN 'الإعدادي'
    END as level_name,
    COUNT(*) as subjects_count
FROM subjects s
WHERE s.year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6', 'grade7', 'grade8', 'grade9')
GROUP BY
    CASE
        WHEN s.year_id IN ('grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6') THEN 'الابتدائي'
        WHEN s.year_id IN ('grade7', 'grade8', 'grade9') THEN 'الإعدادي'
    END
ORDER BY level_name;
