'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Image, Maximize2, FileText, Download, X } from 'lucide-react';
import { ImageViewer } from '@/components/ui/image-viewer';
import { usePdfHandler } from '@/hooks/use-pdf-handler';
import { useIsMobile } from '@/hooks/use-mobile';

interface ContentCardProps {
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
  index: number;
  contentType: 'exercise' | 'homework' | 'summary' | 'exam';
  lessonTitle?: string;
}

const ContentCard = ({
  exerciseImageUrl,
  solutionImageUrl,
  index,
  contentType,
  lessonTitle
}: ContentCardProps) => {
  const [showSolution, setShowSolution] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | undefined>(undefined);
  const [currentAlt, setCurrentAlt] = useState('');
  const isMobile = useIsMobile();

  const { isPdf, handleDownload } = usePdfHandler();

  // Get content type specific labels
  const getLabels = () => {
    switch (contentType) {
      case 'exercise':
        return {
          title: `تمرين ${index + 1}:`,
          contentLabel: 'للتمرين',
          solutionLabel: 'للحل'
        };
      case 'homework':
        return {
          title: `واجب ${index + 1}:`,
          contentLabel: 'للواجب',
          solutionLabel: 'للحل'
        };
      case 'summary':
        return {
          title: `ملخص ${index + 1}:`,
          contentLabel: 'للملخص',
          solutionLabel: 'للحل'
        };
      case 'exam':
        return {
          title: `امتحان ${index + 1}:`,
          contentLabel: 'للامتحان',
          solutionLabel: 'للحل'
        };
      default:
        return {
          title: `عنصر ${index + 1}:`,
          contentLabel: 'للمحتوى',
          solutionLabel: 'للحل'
        };
    }
  };

  const labels = getLabels();

  // Check if solution is available - handle empty strings and null/undefined
  const hasSolution = Boolean(solutionImageUrl && solutionImageUrl.trim() !== '');

  // Debug logging to help identify the issue (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log(`${contentType} ${index + 1}:`, {
      solutionImageUrl: solutionImageUrl || 'undefined',
      hasSolution,
      contentType
    });
  }

  // Reset showSolution when hasSolution changes
  useEffect(() => {
    if (!hasSolution && showSolution) {
      setShowSolution(false);
    }
  }, [hasSolution, showSolution]);

  const toggleSolution = () => {
    if (hasSolution) {
      setShowSolution(!showSolution);
    }
  };

  const openImageViewer = (imageUrl: string | undefined, alt: string) => {
    if (imageUrl) {
      setCurrentImage(imageUrl);
      setCurrentAlt(alt);
      setViewerOpen(true);
    }
  };

  // Generate custom filename for download
  const generateCustomFilename = (isSolution: boolean = false) => {
    const prefix = lessonTitle ? `${lessonTitle} - ` : '';
    const suffix = isSolution ? ' - الحل' : '';
    const typeLabel = contentType === 'exercise' ? 'تمرين' :
                     contentType === 'homework' ? 'واجب' :
                     contentType === 'summary' ? 'ملخص' :
                     contentType === 'exam' ? 'امتحان' : 'عنصر';
    return `${prefix}${typeLabel} ${index + 1}${suffix}.pdf`;
  };

  const renderMediaContent = (url: string, altText: string, isForSolution = false) => {

    if (isPdf(url)) {
      return (
        <div
          className="rounded-md p-4 bg-muted/30 flex flex-col items-center justify-center mx-auto relative"
          style={{ minHeight: '120px' }}
        >
          <FileText className="h-12 w-12 text-primary mb-2" />
          <p className="text-center text-muted-foreground mb-3">
            ملف PDF {isForSolution ? labels.solutionLabel : labels.contentLabel}
          </p>

          <div className="flex flex-wrap gap-2 mt-2 justify-center">
            <button
              onClick={() => openImageViewer(url, altText)}
              className="bg-primary text-primary-foreground hover:bg-primary/90 px-3 py-1.5 rounded-md text-sm flex items-center gap-1"
              title={isMobile ? "عرض في التطبيق" : "عرض ملف PDF"}
            >
              <Eye className="h-4 w-4" />
              {!isMobile && <span>عرض</span>}
            </button>

            <button
              onClick={() => handleDownload(url, generateCustomFilename(isForSolution))}
              className="bg-muted hover:bg-muted/80 text-muted-foreground px-3 py-1.5 rounded-md text-sm flex items-center gap-1"
              title="تحميل ملف PDF"
            >
              <Download className="h-4 w-4" />
              {!isMobile && <span>تحميل</span>}
            </button>
          </div>
        </div>
      );
    }

    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={url}
        alt={altText}
        className="rounded-md max-w-full h-auto cursor-pointer mx-auto block"
        style={{ margin: '0 auto' }}
        onClick={() => openImageViewer(url, altText)}
        onError={(e) => {
          console.error("فشل تحميل الصورة:", url);
          (e.target as HTMLImageElement).style.display = "none";
        }}
      />
    );
  };

  return (
    <>
      <Card className="rtl-card" dir="rtl">
        <CardContent className="p-1">
          <div className="mb-0.5">
            <h3 className="text-xl font-bold mb-0.5 text-primary text-right arabic-heading">{labels.title}</h3>
          </div>

          {/* Main Content */}
          {exerciseImageUrl ? (
            <div className="mb-1 relative group">
              {renderMediaContent(exerciseImageUrl, `صورة توضيحية ${labels.contentLabel} ${index + 1}`)}
              <button
                className="absolute top-0.5 left-0.5 bg-background/80 p-0.5 rounded-md opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => openImageViewer(exerciseImageUrl, isPdf(exerciseImageUrl) ? `ملف PDF ${labels.contentLabel} ${index + 1}` : `صورة توضيحية ${labels.contentLabel} ${index + 1}`)}
                title={isPdf(exerciseImageUrl) ? "عرض ملف PDF" : "عرض الصورة بحجم أكبر"}
              >
                {isPdf(exerciseImageUrl) ? (
                  <FileText className="h-4 w-4 text-primary" />
                ) : (
                  <Maximize2 className="h-4 w-4 text-primary" />
                )}
              </button>
            </div>
          ) : (
            <div className="mb-1 flex items-center justify-center bg-muted rounded-md p-1">
              {/* eslint-disable-next-line jsx-a11y/alt-text */}
              <Image className="h-12 w-12 text-muted-foreground opacity-50" />
            </div>
          )}

          {/* Show solution section only for exercises and homework, not for summaries */}
          {contentType !== 'summary' && (
            <>
              <div className="flex flex-wrap gap-1 mt-1 mb-1 justify-start">
                <Button
                  onClick={toggleSolution}
                  variant="outline"
                  disabled={!hasSolution}
                  className={`rtl-button flex items-center gap-2 transition-all duration-200 ${
                    hasSolution
                      ? "border-primary text-primary hover:bg-primary/20 hover:text-primary focus:text-primary active:text-primary focus-visible:text-primary focus:ring-0 focus:ring-offset-0 focus:outline-none focus:bg-transparent"
                      : "border-muted-foreground/30 text-muted-foreground cursor-not-allowed opacity-60 hover:opacity-60"
                  }`}
                  title={!hasSolution ? "لا يوجد حل متاح لهذا العنصر" : ""}
                >
                  {showSolution ? (
                    <>
                      إخفاء الحل
                      <EyeOff className="w-4 h-4" />
                    </>
                  ) : (
                    <>
                      {hasSolution ? "عرض الحل" : "لا يوجد حل"}
                      {hasSolution ? <Eye className="w-4 h-4" /> : <X className="w-4 h-4" />}
                    </>
                  )}
                </Button>
              </div>

              {showSolution && (
                <div className="mt-1 p-1 rounded-md bg-muted rtl-container">
                  <h4 className="font-bold mb-0.5 text-primary text-right arabic-heading">الحل:</h4>

                  {/* Solution Content */}
                  {solutionImageUrl ? (
                    <div className="mt-0.5 mb-0.5 relative group">
                      {renderMediaContent(solutionImageUrl, `الحل ${labels.solutionLabel} ${index + 1}`, true)}
                      <button
                        className="absolute top-0.5 left-0.5 bg-background/80 p-0.5 rounded-md opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => openImageViewer(solutionImageUrl, isPdf(solutionImageUrl) ? `ملف PDF ${labels.solutionLabel} ${index + 1}` : `الحل ${labels.solutionLabel} ${index + 1}`)}
                        title={isPdf(solutionImageUrl) ? "عرض ملف PDF" : "عرض الصورة بحجم أكبر"}
                      >
                        {isPdf(solutionImageUrl) ? (
                          <FileText className="h-4 w-4 text-primary" />
                        ) : (
                          <Maximize2 className="h-4 w-4 text-primary" />
                        )}
                      </button>
                    </div>
                  ) : (
                    <div className="mb-0.5 flex items-center justify-center bg-muted/50 rounded-md p-1">
                      {/* eslint-disable-next-line jsx-a11y/alt-text */}
                      <Image className="h-8 w-8 text-muted-foreground opacity-50" />
                    </div>
                  )}

                  <p className="text-muted-foreground italic text-right text-xs">
                    {solutionImageUrl && isPdf(solutionImageUrl)
                      ? "يرجى الاطلاع على ملف PDF للحل"
                      : "يرجى الاطلاع على صورة الحل"}
                  </p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Image Viewer */}
      <ImageViewer
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        imageUrl={currentImage || ''}
        alt={currentAlt}
      />
    </>
  );
};

export default ContentCard;
