import { Metadata } from 'next'
import type { Level, Year, Subject, Lesson } from '@/data/types'
import { siteConfig } from '@/lib/config'

const baseUrl = siteConfig.url
const siteName = siteConfig.name

interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  canonical?: string
  noindex?: boolean
  nofollow?: boolean
  ogImage?: string
  structuredData?: object
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonical,
    noindex = false,
    nofollow = false,
    ogImage = '/og-image.png',
    structuredData
  } = config

  // لا نضيف اسم الموقع هنا لأن layout.tsx يتولى ذلك من خلال template
  // Don't add site name here as layout.tsx handles it through template
  const canonicalUrl = canonical ? `${baseUrl}${canonical}` : undefined

  return {
    title: title, // استخدام العنوان كما هو بدون إضافة اسم الموقع
    description,
    keywords: keywords.join(', '),
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: title, // استخدام العنوان الأصلي في Open Graph أيضاً
      description,
      url: canonicalUrl,
      siteName,
      locale: 'ar_MA',
      type: 'website',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: title, // استخدام العنوان الأصلي في Twitter أيضاً
      description,
      images: [ogImage],
    },
    robots: {
      index: !noindex,
      follow: !nofollow,
      googleBot: {
        index: !noindex,
        follow: !nofollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    other: structuredData ? {
      'script:ld+json': JSON.stringify(structuredData)
    } : undefined,
  }
}

export function generateLevelMetadata(level: Level): Metadata {
  return generateMetadata({
    title: `${level.name} - المراحل الدراسية`,
    description: `استكشف ${level.name} - ${level.description}. دروس وتمارين تفاعلية لجميع السنوات الدراسية`,
    keywords: [
      level.name,
      'مرحلة دراسية',
      'تعليم',
      'دروس',
      'تمارين',
      'مناهج عربية'
    ],
    canonical: `/levels`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Course",
      "name": level.name,
      "description": level.description,
      "provider": {
        "@type": "Organization",
        "name": siteName
      },
      "educationalLevel": level.name,
      "inLanguage": "ar"
    }
  })
}

export function generateYearMetadata(year: Year, level?: Level): Metadata {
  const levelName = level ? level.name : 'المرحلة الدراسية'
  
  return generateMetadata({
    title: `${year.name} - ${levelName}`,
    description: `${year.description || `استكشف ${year.name} في ${levelName}`}. دروس وتمارين تفاعلية لجميع المواد الدراسية`,
    keywords: [
      year.name,
      levelName,
      'سنة دراسية',
      'مواد دراسية',
      'تعليم',
      'دروس',
      'تمارين'
    ],
    canonical: `/year/${year.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Course",
      "name": year.name,
      "description": year.description,
      "provider": {
        "@type": "Organization",
        "name": siteName
      },
      "educationalLevel": levelName,
      "courseCode": year.id,
      "inLanguage": "ar"
    }
  })
}

export function generateSubjectMetadata(subject: Subject, year?: Year): Metadata {
  const yearName = year ? year.name : 'السنة الدراسية'
  
  return generateMetadata({
    title: `${subject.name} - ${yearName}`,
    description: `تعلم ${subject.name} في ${yearName}. دروس تفاعلية وتمارين شاملة مع الحلول والملخصات`,
    keywords: [
      subject.name,
      yearName,
      'مادة دراسية',
      'دروس',
      'تمارين',
      'ملخصات',
      'امتحانات',
      'فروض'
    ],
    canonical: `/subject/${subject.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Course",
      "name": subject.name,
      "description": `دروس ${subject.name} في ${yearName}`,
      "provider": {
        "@type": "Organization",
        "name": siteName
      },
      "courseCode": subject.id,
      "educationalLevel": yearName,
      "inLanguage": "ar",
      "teaches": subject.name
    }
  })
}

export function generateLessonMetadata(lesson: Lesson, subject?: Subject): Metadata {
  const subjectName = subject ? subject.name : 'المادة الدراسية'
  
  return generateMetadata({
    title: `${lesson.title} - ${subjectName}`,
    description: lesson.description || `تعلم ${lesson.title} في ${subjectName}. تمارين تفاعلية مع الحلول والشروحات المفصلة`,
    keywords: [
      lesson.title,
      subjectName,
      'درس',
      'تمارين',
      'حلول',
      'شرح',
      'تعليم تفاعلي'
    ],
    canonical: `/lesson/${lesson.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "LearningResource",
      "name": lesson.title,
      "description": lesson.description,
      "educationalLevel": subjectName,
      "learningResourceType": "درس تفاعلي",
      "inLanguage": "ar",
      "provider": {
        "@type": "Organization",
        "name": siteName
      },
      "isPartOf": {
        "@type": "Course",
        "name": subjectName
      }
    }
  })
}

export function generateHomeworkMetadata(lesson: Lesson, subject?: Subject): Metadata {
  const subjectName = subject ? subject.name : 'المادة الدراسية'
  
  return generateMetadata({
    title: `فروض ${lesson.title} - ${subjectName}`,
    description: `فروض منزلية لدرس ${lesson.title} في ${subjectName}. تمارين تطبيقية لتعزيز الفهم`,
    keywords: [
      'فروض منزلية',
      lesson.title,
      subjectName,
      'تمارين',
      'تطبيق',
      'مراجعة'
    ],
    canonical: `/homework/${lesson.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "LearningResource",
      "name": `فروض ${lesson.title}`,
      "description": `فروض منزلية لدرس ${lesson.title}`,
      "educationalLevel": subjectName,
      "learningResourceType": "فروض منزلية",
      "inLanguage": "ar",
      "provider": {
        "@type": "Organization",
        "name": siteName
      }
    }
  })
}

export function generateSummaryMetadata(lesson: Lesson, subject?: Subject): Metadata {
  const subjectName = subject ? subject.name : 'المادة الدراسية'
  
  return generateMetadata({
    title: `ملخص ${lesson.title} - ${subjectName}`,
    description: `ملخص شامل لدرس ${lesson.title} في ${subjectName}. النقاط الأساسية والمفاهيم المهمة`,
    keywords: [
      'ملخص',
      lesson.title,
      subjectName,
      'مراجعة',
      'نقاط مهمة',
      'مفاهيم أساسية'
    ],
    canonical: `/summary/${lesson.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "LearningResource",
      "name": `ملخص ${lesson.title}`,
      "description": `ملخص شامل لدرس ${lesson.title}`,
      "educationalLevel": subjectName,
      "learningResourceType": "ملخص درس",
      "inLanguage": "ar",
      "provider": {
        "@type": "Organization",
        "name": siteName
      }
    }
  })
}

export function generateExamMetadata(lesson: Lesson, subject?: Subject): Metadata {
  const subjectName = subject ? subject.name : 'المادة الدراسية'
  
  return generateMetadata({
    title: `امتحان ${lesson.title} - ${subjectName}`,
    description: `امتحان تفاعلي لدرس ${lesson.title} في ${subjectName}. اختبر معلوماتك وقيم مستواك`,
    keywords: [
      'امتحان',
      lesson.title,
      subjectName,
      'اختبار',
      'تقييم',
      'أسئلة'
    ],
    canonical: `/exam/${lesson.id}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "LearningResource",
      "name": `امتحان ${lesson.title}`,
      "description": `امتحان تفاعلي لدرس ${lesson.title}`,
      "educationalLevel": subjectName,
      "learningResourceType": "امتحان",
      "inLanguage": "ar",
      "provider": {
        "@type": "Organization",
        "name": siteName
      }
    }
  })
}
