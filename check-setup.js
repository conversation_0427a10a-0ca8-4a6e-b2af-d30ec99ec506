#!/usr/bin/env node

// Arab Education Exercises - Setup Verification Script
// سكريبت التحقق من إعداد المشروع

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص إعداد مشروع التمارين التعليمية العربية...\n');

const checks = [
  {
    name: 'Node.js Version',
    check: () => {
      const version = process.version;
      const major = parseInt(version.slice(1).split('.')[0]);
      return {
        passed: major >= 18,
        message: `إصدار Node.js: ${version} ${major >= 18 ? '✅' : '❌ (مطلوب 18+)'}`
      };
    }
  },
  {
    name: 'Package.json',
    check: () => {
      const exists = fs.existsSync('package.json');
      return {
        passed: exists,
        message: `ملف package.json: ${exists ? '✅ موجود' : '❌ غير موجود'}`
      };
    }
  },
  {
    name: 'Server.js',
    check: () => {
      const exists = fs.existsSync('server.js');
      return {
        passed: exists,
        message: `ملف server.js: ${exists ? '✅ موجود' : '❌ غير موجود'}`
      };
    }
  },
  {
    name: 'Next.js Config',
    check: () => {
      const exists = fs.existsSync('next.config.js');
      return {
        passed: exists,
        message: `ملف next.config.js: ${exists ? '✅ موجود' : '❌ غير موجود'}`
      };
    }
  },
  {
    name: 'Dependencies',
    check: () => {
      const exists = fs.existsSync('node_modules');
      return {
        passed: exists,
        message: `مجلد node_modules: ${exists ? '✅ موجود' : '⚠️  غير موجود (تشغيل npm install)'}`
      };
    }
  },
  {
    name: 'Build Directory',
    check: () => {
      const exists = fs.existsSync('.next');
      return {
        passed: exists,
        message: `مجلد البناء .next: ${exists ? '✅ موجود' : '⚠️  غير موجود (تشغيل npm run build)'}`
      };
    }
  },
  {
    name: 'Environment Variables',
    check: () => {
      const envLocal = fs.existsSync('.env.local');
      const env = fs.existsSync('.env');
      const exists = envLocal || env;
      return {
        passed: exists,
        message: `متغيرات البيئة: ${exists ? '✅ موجودة' : '⚠️  غير موجودة (إنشاء .env.local)'}`
      };
    }
  },
  {
    name: 'Public Directory',
    check: () => {
      const exists = fs.existsSync('public');
      return {
        passed: exists,
        message: `مجلد public: ${exists ? '✅ موجود' : '❌ غير موجود'}`
      };
    }
  },
  {
    name: 'Required Dependencies',
    check: () => {
      try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const required = ['express', 'compression', 'helmet', 'next', 'react'];
        const missing = required.filter(dep => 
          !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
        );
        
        return {
          passed: missing.length === 0,
          message: `التبعيات المطلوبة: ${missing.length === 0 ? '✅ جميعها موجودة' : `❌ مفقودة: ${missing.join(', ')}`}`
        };
      } catch (error) {
        return {
          passed: false,
          message: '❌ خطأ في قراءة package.json'
        };
      }
    }
  }
];

let allPassed = true;
const results = [];

checks.forEach(check => {
  const result = check.check();
  results.push(result);
  console.log(`${result.message}`);
  if (!result.passed) allPassed = false;
});

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 جميع الفحوصات نجحت! المشروع جاهز للنشر.');
  console.log('\n📋 الخطوات التالية:');
  console.log('1. تأكد من إعداد متغيرات البيئة في .env.local');
  console.log('2. قم ببناء المشروع: npm run build');
  console.log('3. ابدأ الخادم: npm start');
  console.log('4. أو استخدم: ./start.sh');
} else {
  console.log('⚠️  هناك مشاكل تحتاج إلى حل قبل النشر.');
  console.log('\n🔧 الإجراءات المطلوبة:');
  
  results.forEach((result, index) => {
    if (!result.passed) {
      const check = checks[index];
      console.log(`- ${check.name}: ${result.message}`);
    }
  });
}

console.log('\n📖 للمزيد من المعلومات، راجع ملف CPANEL_DEPLOYMENT.md');

process.exit(allPassed ? 0 : 1);
