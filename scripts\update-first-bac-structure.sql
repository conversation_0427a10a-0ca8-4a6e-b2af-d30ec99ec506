-- سكريبت تحديث بنية الأولى باك في Supabase
-- تحويل الأولى باك إلى التخصصات المغربية الفعلية

-- 1. حذف السنوات الحالية للأولى باك والمواد المرتبطة بها
DELETE FROM subjects WHERE year_id IN ('first_bac_sciences', 'first_bac_literature', 'first_bac_economics');
DELETE FROM years WHERE id IN ('first_bac_sciences', 'first_bac_literature', 'first_bac_economics');

-- 2. تحديث مستوى الأولى باك ليحتوي على التخصصات المغربية الجديدة
UPDATE levels
SET years = ARRAY['first_bac_sciences_math', 'first_bac_sciences_exp', 'first_bac_tech_mech', 'first_bac_tech_elec', 'first_bac_econ_mgmt', 'first_bac_humanities'],
    description = 'السنة الثانية من التعليم الثانوي مع التخصصات المغربية'
WHERE id = 'first_bac';

-- 3. إضافة السنوات الجديدة للتخصصات المغربية
INSERT INTO years (id, name, level_id, subjects) VALUES
('first_bac_sciences_math', 'الأولى بكالوريا - العلوم الرياضية', 'first_bac', ARRAY[]::text[]),
('first_bac_sciences_exp', 'الأولى بكالوريا - العلوم التجريبية', 'first_bac', ARRAY[]::text[]),
('first_bac_tech_mech', 'الأولى بكالوريا - العلوم والتكنولوجيات الميكانيكية', 'first_bac', ARRAY[]::text[]),
('first_bac_tech_elec', 'الأولى بكالوريا - العلوم والتكنولوجيات الكهربائية', 'first_bac', ARRAY[]::text[]),
('first_bac_econ_mgmt', 'الأولى بكالوريا - العلوم الاقتصادية والتدبير', 'first_bac', ARRAY[]::text[]),
('first_bac_humanities', 'الأولى بكالوريا - الآداب والعلوم الإنسانية', 'first_bac', ARRAY[]::text[]);

-- 4. إضافة مواد العلوم الرياضية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_math_math', 'الرياضيات', '📐', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_physics', 'الفيزياء والكيمياء', '⚛️', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_biology', 'علوم الحياة والأرض', '🌱', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_arabic', 'اللغة العربية', '📚', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_philosophy', 'الفلسفة', '🤔', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_islamic', 'التربية الإسلامية', '☪️', 'first_bac_sciences_math', ARRAY[]::text[]),
('first_math_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_sciences_math', ARRAY[]::text[]);

-- 5. إضافة مواد العلوم التجريبية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_exp_biology', 'علوم الحياة والأرض', '🌱', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_physics', 'الفيزياء والكيمياء', '⚛️', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_math', 'الرياضيات', '📐', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_arabic', 'اللغة العربية', '📚', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_philosophy', 'الفلسفة', '🤔', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_islamic', 'التربية الإسلامية', '☪️', 'first_bac_sciences_exp', ARRAY[]::text[]),
('first_exp_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_sciences_exp', ARRAY[]::text[]);

-- 6. إضافة مواد العلوم والتكنولوجيات الميكانيكية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_mech_engineering', 'الهندسة الميكانيكية', '🔧', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_math', 'الرياضيات', '📐', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_physics', 'الفيزياء والكيمياء', '⚛️', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_technology', 'التكنولوجيا الصناعية', '⚙️', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_arabic', 'اللغة العربية', '📚', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_philosophy', 'الفلسفة', '🤔', 'first_bac_tech_mech', ARRAY[]::text[]),
('first_mech_islamic', 'التربية الإسلامية', '☪️', 'first_bac_tech_mech', ARRAY[]::text[]);

-- 7. إضافة مواد العلوم والتكنولوجيات الكهربائية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_elec_engineering', 'الهندسة الكهربائية', '⚡', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_math', 'الرياضيات', '📐', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_physics', 'الفيزياء والكيمياء', '⚛️', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_technology', 'التكنولوجيا الصناعية', '⚙️', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_arabic', 'اللغة العربية', '📚', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_philosophy', 'الفلسفة', '🤔', 'first_bac_tech_elec', ARRAY[]::text[]),
('first_elec_islamic', 'التربية الإسلامية', '☪️', 'first_bac_tech_elec', ARRAY[]::text[]);

-- 8. إضافة مواد العلوم الاقتصادية والتدبير
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_econ_economics', 'الاقتصاد العام', '💼', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_management', 'التدبير المحاسباتي', '📊', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_math', 'الرياضيات', '📐', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_arabic', 'اللغة العربية', '📚', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_philosophy', 'الفلسفة', '🤔', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_islamic', 'التربية الإسلامية', '☪️', 'first_bac_econ_mgmt', ARRAY[]::text[]),
('first_econ_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_econ_mgmt', ARRAY[]::text[]);

-- 9. إضافة مواد الآداب والعلوم الإنسانية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_hum_arabic', 'اللغة العربية', '📚', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_philosophy', 'الفلسفة', '🤔', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_islamic', 'التربية الإسلامية', '☪️', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_sociology', 'علم الاجتماع', '👥', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_literature', 'الأدب العربي', '📖', 'first_bac_humanities', ARRAY[]::text[]),
('first_hum_math', 'الرياضيات', '📐', 'first_bac_humanities', ARRAY[]::text[]);

-- 10. تحديث مصفوفة subjects في جدول years
UPDATE years SET subjects = ARRAY[
    'first_math_math', 'first_math_physics', 'first_math_biology', 'first_math_arabic',
    'first_math_french', 'first_math_english', 'first_math_philosophy', 'first_math_islamic', 'first_math_history'
] WHERE id = 'first_bac_sciences_math';

UPDATE years SET subjects = ARRAY[
    'first_exp_biology', 'first_exp_physics', 'first_exp_math', 'first_exp_arabic',
    'first_exp_french', 'first_exp_english', 'first_exp_philosophy', 'first_exp_islamic', 'first_exp_history'
] WHERE id = 'first_bac_sciences_exp';

UPDATE years SET subjects = ARRAY[
    'first_mech_engineering', 'first_mech_math', 'first_mech_physics', 'first_mech_technology',
    'first_mech_arabic', 'first_mech_french', 'first_mech_english', 'first_mech_philosophy', 'first_mech_islamic'
] WHERE id = 'first_bac_tech_mech';

UPDATE years SET subjects = ARRAY[
    'first_elec_engineering', 'first_elec_math', 'first_elec_physics', 'first_elec_technology',
    'first_elec_arabic', 'first_elec_french', 'first_elec_english', 'first_elec_philosophy', 'first_elec_islamic'
] WHERE id = 'first_bac_tech_elec';

UPDATE years SET subjects = ARRAY[
    'first_econ_economics', 'first_econ_management', 'first_econ_math', 'first_econ_arabic',
    'first_econ_french', 'first_econ_english', 'first_econ_philosophy', 'first_econ_islamic', 'first_econ_history'
] WHERE id = 'first_bac_econ_mgmt';

UPDATE years SET subjects = ARRAY[
    'first_hum_arabic', 'first_hum_french', 'first_hum_english', 'first_hum_history',
    'first_hum_philosophy', 'first_hum_islamic', 'first_hum_sociology', 'first_hum_literature', 'first_hum_math'
] WHERE id = 'first_bac_humanities';

-- 11. عرض النتائج للتأكد من التحديث
SELECT 'المستوى المحدث:' as info;
SELECT id, name, description, array_length(years, 1) as years_count FROM levels WHERE id = 'first_bac';

SELECT 'السنوات الجديدة للأولى باك:' as info;
SELECT id, name, level_id FROM years WHERE level_id = 'first_bac' ORDER BY id;

SELECT 'المواد الدراسية الجديدة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN (
    'first_bac_sciences_math', 'first_bac_sciences_exp', 'first_bac_tech_mech',
    'first_bac_tech_elec', 'first_bac_econ_mgmt', 'first_bac_humanities'
) ORDER BY year_id, name;

-- 12. إحصائيات التحديث
SELECT
    'إحصائيات التحديث' as info,
    (SELECT COUNT(*) FROM years WHERE level_id = 'first_bac') as total_years,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN (
        'first_bac_sciences_math', 'first_bac_sciences_exp', 'first_bac_tech_mech',
        'first_bac_tech_elec', 'first_bac_econ_mgmt', 'first_bac_humanities'
    )) as total_subjects;

-- 13. التحقق من البيانات النهائية
SELECT 'التحقق النهائي من البنية:' as final_check;

-- عرض المستوى المحدث
SELECT 'مستوى الأولى باك:' as section, id, name, description, array_length(years, 1) as years_count
FROM levels WHERE id = 'first_bac';

-- عرض السنوات الست
SELECT 'السنوات الست:' as section, id, name, level_id
FROM years WHERE level_id = 'first_bac' ORDER BY id;

-- عرض عدد المواد لكل تخصص
SELECT
    'عدد المواد لكل تخصص:' as section,
    year_id as specialization,
    COUNT(*) as subjects_count
FROM subjects
WHERE year_id IN (
    'first_bac_sciences_math', 'first_bac_sciences_exp', 'first_bac_tech_mech',
    'first_bac_tech_elec', 'first_bac_econ_mgmt', 'first_bac_humanities'
)
GROUP BY year_id
ORDER BY year_id;
