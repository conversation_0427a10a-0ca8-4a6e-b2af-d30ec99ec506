# إدارة Sitemap.xml

## كيف يعمل تحديث Sitemap.xml

### 🔄 التحديث التلقائي

يتم تحديث `sitemap.xml` تلقائياً في الحالات التالية:

1. **في بيئة التطوير**: مع كل طلب جديد
2. **في بيئة الإنتاج**: كل ساعة (3600 ثانية) أو عند الطلب

### 📊 البيانات المشمولة في Sitemap

- **الصفحات الثابتة**: الرئيسية، المستويات، حول الموقع
- **صفحات السنوات الدراسية**: `/year/[yearId]`
- **صفحات المواد الدراسية**: `/subject/[subjectId]`
- **صفحات الدروس**: `/lesson/[lessonId]`
- **صفحات الواجبات**: `/homework/[lessonId]`
- **صفحات الملخصات**: `/summary/[lessonId]`
- **صفحات الامتحانات**: `/exam/[lessonId]`

### 🚀 تحديث فوري عند إضافة بيانات جديدة

#### 1. استخدام API Route

```bash
# تحديث sitemap.xml فورياً
curl -X POST http://localhost:3000/api/revalidate-sitemap

# مع token أمان (اختياري)
curl -X POST http://localhost:3000/api/revalidate-sitemap \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. استخدام الدوال المساعدة

```typescript
import { triggerSitemapUpdate } from '@/utils/sitemap-revalidation'

// بعد إضافة سنة دراسية جديدة
await triggerSitemapUpdate('add', 'year', newYearId)

// بعد إضافة مادة دراسية جديدة
await triggerSitemapUpdate('add', 'subject', newSubjectId)

// بعد إضافة درس جديد
await triggerSitemapUpdate('add', 'lesson', newLessonId)

// بعد حذف عنصر
await triggerSitemapUpdate('delete', 'subject', deletedSubjectId)

// بعد تحديث عنصر
await triggerSitemapUpdate('update', 'year', updatedYearId)
```

#### 3. استخدام Data Manager

```typescript
import { addNewYear, addNewSubject, addNewLesson } from '@/backend/utils/dataManager'

// إضافة سنة جديدة (مع تحديث sitemap تلقائياً)
await addNewYear({
  id: 'new-year-id',
  name: 'السنة الجديدة',
  levelId: 'level-id'
})

// إضافة مادة جديدة (مع تحديث sitemap تلقائياً)
await addNewSubject({
  id: 'new-subject-id',
  name: 'المادة الجديدة',
  yearId: 'year-id'
})
```

### ⚙️ الإعدادات

#### متغيرات البيئة

```env
# اختياري: token لحماية API تحديث sitemap
REVALIDATE_TOKEN=your_secure_random_token_here
```

#### تخصيص فترة التحديث

في `app/sitemap.ts`:

```typescript
// تحديث كل 30 دقيقة
export const revalidate = 1800

// تحديث كل 6 ساعات
export const revalidate = 21600

// تحديث يومياً
export const revalidate = 86400
```

### 🔍 مراقبة التحديثات

#### في الكونسول

```
✅ تم تحديث sitemap.xml: تم تحديث sitemap.xml بنجاح
🔄 تحديث sitemap.xml بعد add subject (new-subject-id)
✅ تم تحديث sitemap.xml بنجاح بعد add subject
```

#### في ملفات السجل

```
Sitemap: تم جلب 5 مستويات، 29 سنوات، 251 مواد، 5 دروس
Sitemap: تم إنشاء 292 صفحة في sitemap
```

### 🛠️ استكشاف الأخطاء

#### مشكلة: sitemap.xml لا يتحدث

1. تحقق من الكونسول للأخطاء
2. تأكد من اتصال Supabase
3. جرب التحديث اليدوي:
   ```bash
   curl -X POST http://localhost:3000/api/revalidate-sitemap
   ```

#### مشكلة: بيانات قديمة في sitemap

1. امسح الذاكرة المؤقتة:
   ```typescript
   import { clearAllCacheAndRefresh } from '@/backend/utils/dataLoader'
   clearAllCacheAndRefresh()
   ```

2. أعد تشغيل الخادم في بيئة التطوير

### 📈 الأداء

- **حجم sitemap.xml**: حوالي 292 صفحة حالياً
- **وقت التوليد**: 2-3 ثوانٍ
- **استهلاك الذاكرة**: منخفض
- **تأثير على الأداء**: ضئيل جداً

### 🔮 المستقبل

- إضافة sitemap index للمواقع الكبيرة
- تحديث تلقائي عبر webhooks من Supabase
- ضغط sitemap.xml للمواقع الكبيرة
- إضافة lastmod دقيق لكل صفحة
