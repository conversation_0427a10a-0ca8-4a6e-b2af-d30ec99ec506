/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.talamid.ma',
  generateRobotsTxt: false, // We have a custom robots.txt
  generateIndexSitemap: true,
  exclude: [
    '/admin/*',
    '/api/*',
    '/uploads/*',
    '/_next/*',
    '/test-*'
  ],
  alternateRefs: [
    {
      href: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.talamid.ma',
      hreflang: 'ar',
    },
    {
      href: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.talamid.ma',
      hreflang: 'ar-MA',
    },
  ],
  transform: async (config, path) => {
    // Custom priority and changefreq based on path
    let priority = 0.7;
    let changefreq = 'weekly';

    if (path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    } else if (path === '/levels') {
      priority = 0.9;
      changefreq = 'daily';
    } else if (path.startsWith('/year/')) {
      priority = 0.8;
      changefreq = 'weekly';
    } else if (path.startsWith('/subject/')) {
      priority = 0.7;
      changefreq = 'weekly';
    } else if (path.startsWith('/lesson/')) {
      priority = 0.6;
      changefreq = 'weekly';
    } else if (path.includes('/homework/') || path.includes('/summary/') || path.includes('/exam/')) {
      priority = 0.5;
      changefreq = 'weekly';
    } else if (path === '/about') {
      priority = 0.5;
      changefreq = 'monthly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
      alternateRefs: config.alternateRefs ?? [],
    };
  },
  additionalPaths: async (config) => {
    // Add any additional static paths here
    return [
      await config.transform(config, '/'),
      await config.transform(config, '/levels'),
      await config.transform(config, '/about'),
    ];
  },
};
