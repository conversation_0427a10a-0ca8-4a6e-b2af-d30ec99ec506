'use client';

import TableContentList from '@/components/ui/table-content-list';
import { Exercise } from '@/data/types';

interface HomeworkListProps {
  exercises: Exercise[];
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

const HomeworkList = ({
  exercises,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: HomeworkListProps) => {
  return (
    <TableContentList
      exercises={exercises}
      contentType="homework"
      lessonTitle={lessonTitle}
      subjectName={subjectName}
      yearName={yearName}
      levelName={levelName}
    />
  );
};

export default HomeworkList;
