import { supabase } from "@/integrations/supabase/client";
import { Level, Year, Subject, Lesson, Exercise, Exam } from '@/data/types';
import { resolveFileUrl } from '@/utils/file-url-resolver';
import { sanitizeIdForDatabase } from '@/utils/id-utils';

// جلب جميع المستويات من Supabase مرتبة حسب display_order
export async function fetchLevelsFromSupabase(): Promise<Level[]> {
  try {
    const { data, error } = await supabase
      .from('levels')
      .select('*')
      .order('display_order', { ascending: true });

    if (error) {
      console.error('خطأ في جلب المستويات من Supabase:', error);
      return [];
    }

    // Map database data to match our type expectations
    const mappedLevels = (data || []).map(level => ({
      id: level.id,
      name: level.name,
      description: level.description
    }));

    console.log(`تم جلب ${mappedLevels.length} مستوى بنجاح مرتبة حسب الترتيب التعليمي`);
    return mappedLevels;
  } catch (e) {
    console.error('خطأ غير متوقع في جلب المستويات:', e);
    return [];
  }
}

// جلب جميع السنوات من Supabase
export async function fetchYearsFromSupabase(): Promise<Year[]> {
  try {
    const { data, error } = await supabase
      .from('years')
      .select('*');

    if (error) {
      console.error('خطأ في جلب السنوات من Supabase:', error);
      return [];
    }

    // Map database field names to match our type expectations
    const mappedYears = (data || []).map(year => ({
      id: year.id,
      name: year.name,
      levelId: year.level_id || '', // Handle null values
      description: year.description || 'استكشف المواد الدراسية لهذه السنة' // Handle null values
    }));

    console.log(`تم جلب ${mappedYears.length} سنة بنجاح`);
    return mappedYears;
  } catch (e) {
    console.error('خطأ غير متوقع في جلب السنوات:', e);
    return [];
  }
}

// جلب مستوى واحد مباشرة من Supabase
export async function fetchLevelFromSupabase(levelId: string): Promise<Level | null> {
  try {
    const sanitizedLevelId = sanitizeIdForDatabase(levelId);
    console.log(`جاري جلب المستوى ${sanitizedLevelId} مباشرة من Supabase...`);

    // جلب المستوى المحدد فقط
    const { data: levelData, error: levelError } = await supabase
      .from('levels')
      .select('*')
      .eq('id', sanitizedLevelId)
      .single();

    if (levelError) {
      console.error(`خطأ في جلب المستوى ${levelId} من Supabase:`, levelError);
      return null;
    }

    if (!levelData) {
      console.log(`لم يتم العثور على المستوى ${levelId}`);
      return null;
    }

    // تحويل البيانات إلى التنسيق المطلوب
    const level: Level = {
      id: levelData.id,
      name: levelData.name,
      description: levelData.description || ''
    };

    console.log(`تم جلب المستوى "${level.name}" بنجاح`);
    return level;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب المستوى ${levelId}:`, e);
    return null;
  }
}

// جلب السنوات حسب المستوى من Supabase
export async function fetchYearsForLevelFromSupabase(levelId: string): Promise<Year[]> {
  try {
    const sanitizedLevelId = sanitizeIdForDatabase(levelId);
    const { data, error } = await supabase
      .from('years')
      .select('*')
      .eq('level_id', sanitizedLevelId);

    if (error) {
      console.error(`خطأ في جلب السنوات للمستوى ${levelId} من Supabase:`, error);
      return [];
    }

    // Map database field names to match our type expectations
    const mappedYears = (data || []).map(year => ({
      id: year.id,
      name: year.name,
      levelId: year.level_id || '', // Handle null values
      description: year.description || 'استكشف المواد الدراسية لهذه السنة' // Handle null values
    }));

    console.log(`تم جلب ${mappedYears.length} سنة للمستوى ${levelId} بنجاح`);
    return mappedYears;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب السنوات للمستوى ${levelId}:`, e);
    return [];
  }
}

// جلب سنة واحدة مباشرة من Supabase
export async function fetchYearFromSupabase(yearId: string): Promise<Year | null> {
  try {
    const sanitizedYearId = sanitizeIdForDatabase(yearId);
    console.log(`جاري جلب السنة ${sanitizedYearId} مباشرة من Supabase...`);

    // جلب السنة المحددة فقط
    const { data: yearData, error: yearError } = await supabase
      .from('years')
      .select('*')
      .eq('id', sanitizedYearId)
      .single();

    if (yearError) {
      console.error(`خطأ في جلب السنة ${yearId} من Supabase:`, yearError);
      return null;
    }

    if (!yearData) {
      console.log(`لم يتم العثور على السنة ${yearId}`);
      return null;
    }

    // تحويل البيانات إلى التنسيق المطلوب
    const year: Year = {
      id: yearData.id,
      name: yearData.name,
      levelId: yearData.level_id || '',
      description: yearData.description || 'استكشف المواد الدراسية لهذه السنة'
    };

    console.log(`تم جلب السنة "${year.name}" بنجاح`);
    return year;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب السنة ${yearId}:`, e);
    return null;
  }
}

// جلب جميع المواد من Supabase
export async function fetchSubjectsFromSupabase(): Promise<Subject[]> {
  try {
    const { data, error } = await supabase
      .from('subjects')
      .select('*');

    if (error) {
      console.error('خطأ في جلب المواد من Supabase:', error);
      return [];
    }

    // Map database field names to match our type expectations
    const mappedSubjects = (data || []).map(subject => ({
      id: subject.id,
      name: subject.name,
      icon: subject.icon,
      yearId: subject.year_id || '', // Handle null values
      description: subject.description || undefined // Handle null values
    }));

    console.log(`تم جلب ${mappedSubjects.length} مادة بنجاح`);
    return mappedSubjects;
  } catch (e) {
    console.error('خطأ غير متوقع في جلب المواد:', e);
    return [];
  }
}

// جلب مادة واحدة مباشرة من Supabase
export async function fetchSubjectFromSupabase(subjectId: string): Promise<Subject | null> {
  try {
    const sanitizedSubjectId = sanitizeIdForDatabase(subjectId);
    console.log(`جاري جلب المادة ${sanitizedSubjectId} مباشرة من Supabase...`);

    // جلب المادة المحددة فقط
    const { data: subjectData, error: subjectError } = await supabase
      .from('subjects')
      .select('*')
      .eq('id', sanitizedSubjectId)
      .single();

    if (subjectError) {
      console.error(`خطأ في جلب المادة ${subjectId} من Supabase:`, subjectError);
      return null;
    }

    if (!subjectData) {
      console.log(`لم يتم العثور على المادة ${subjectId}`);
      return null;
    }

    // تحويل البيانات إلى التنسيق المطلوب
    const subject: Subject = {
      id: subjectData.id,
      name: subjectData.name,
      icon: subjectData.icon,
      yearId: subjectData.year_id || '',
      description: subjectData.description || undefined
    };

    console.log(`تم جلب المادة "${subject.name}" بنجاح`);
    return subject;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب المادة ${subjectId}:`, e);
    return null;
  }
}

// جلب المواد حسب السنة من Supabase
export async function fetchSubjectsForYearFromSupabase(yearId: string): Promise<Subject[]> {
  try {
    const sanitizedYearId = sanitizeIdForDatabase(yearId);
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('year_id', sanitizedYearId);

    if (error) {
      console.error(`خطأ في جلب المواد للسنة ${yearId} من Supabase:`, error);
      return [];
    }

    // Map database field names to match our type expectations
    const mappedSubjects = (data || []).map(subject => ({
      id: subject.id,
      name: subject.name,
      icon: subject.icon,
      yearId: subject.year_id || '', // Handle null values
      description: subject.description || undefined // Handle null values
    }));

    console.log(`تم جلب ${mappedSubjects.length} مادة للسنة ${yearId} بنجاح`);
    return mappedSubjects;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب المواد للسنة ${yearId}:`, e);
    return [];
  }
}

// دالة مساعدة لجلب التمارين/الواجبات/الامتحانات لدرس واحد
async function fetchExercisesForLesson(lesson: any): Promise<Exercise[]> {
  try {
    let exercisesData: any[] = [];
    let error = null;

    console.log(`جاري جلب التمارين للدرس ${lesson.id} من نوع ${lesson.content_type}`);

    // جلب التمارين حسب نوع المحتوى (تمارين أو واجبات أو ملخصات أو امتحانات)
    // نحدد الحقول المطلوبة فقط لتحسين الأداء

    if (lesson.content_type === 'exercise') {
      const response = await supabase
        .from('exercises')
        .select('id, hint, exercise_image_url, solution_image_url')
        .eq('lesson_id', lesson.id);

      exercisesData = response.data || [];
      error = response.error;
      console.log(`تم العثور على ${exercisesData.length} تمارين للدرس ${lesson.id}`);
    }
    else if (lesson.content_type === 'homework') {
      const response = await supabase
        .from('homeworks')
        .select('id, hint, exercise_image_url, solution_image_url')
        .eq('lesson_id', lesson.id);

      exercisesData = response.data || [];
      error = response.error;
      console.log(`تم العثور على ${exercisesData.length} واجبات للدرس ${lesson.id}`);
    }
    else if (lesson.content_type === 'summary') {
      const response = await supabase
        .from('summaries')
        .select('id, hint, exercise_image_url')
        .eq('lesson_id', lesson.id);

      exercisesData = response.data || [];
      error = response.error;
      console.log(`تم العثور على ${exercisesData.length} ملخصات للدرس ${lesson.id}`);
    }
    else if (lesson.content_type === 'exam') {
      const response = await supabase
        .from('exams')
        .select('id, hint, exercise_image_url, solution_image_url')
        .eq('lesson_id', lesson.id);

      exercisesData = response.data || [];
      error = response.error;
      console.log(`تم العثور على ${exercisesData.length} امتحانات للدرس ${lesson.id}`);
    }

    if (error) {
      console.error(`خطأ في جلب التمارين/الواجبات/الملخصات للدرس ${lesson.id}:`, error);
      return [];
    }

    // تحويل البيانات إلى التنسيق المطلوب مع تحويل الروابط النسبية إلى مطلقة
    const mappedExercises = (exercisesData || []).map(exercise => ({
      id: exercise.id,
      hint: exercise.hint || '',
      exerciseImageUrl: resolveFileUrl(exercise.exercise_image_url) || '',
      // للملخصات، لا نحتاج solution_image_url
      solutionImageUrl: lesson.content_type === 'summary' ? undefined : (resolveFileUrl(exercise.solution_image_url) || ''),
    }));

    console.log(`تم تحويل ${mappedExercises.length} تمارين للدرس ${lesson.id}`);
    return mappedExercises;

  } catch (e) {
    console.error(`خطأ غير متوقع أثناء معالجة التمارين للدرس ${lesson.id}:`, e);
    return [];
  }
}

// دالة مساعدة لتحويل بيانات الدرس إلى التنسيق المطلوب
function mapLessonData(lesson: any, exercises: Exercise[]): Lesson {
  return {
    id: lesson.id,
    title: lesson.title,
    description: lesson.description,
    subjectId: lesson.subject_id || '', // Handle null values
    content_type: (lesson.content_type || 'exercise') as 'exercise' | 'homework' | 'summary' | 'exam',
    exercises: exercises,
    display_order: lesson.display_order || 0
  };
}

// جلب جميع الدروس من Supabase
export async function fetchLessonsFromSupabase(): Promise<Lesson[]> {
  try {
    // جلب جميع الدروس مرتبة حسب display_order
    const { data: lessonsData, error: lessonsError } = await supabase
      .from('lessons')
      .select('*')
      .order('display_order', { ascending: true });

    if (lessonsError) {
      console.error('خطأ في جلب الدروس من Supabase:', lessonsError);
      return [];
    }

    if (!lessonsData || lessonsData.length === 0) {
      console.log('لا توجد دروس في قاعدة البيانات');
      return [];
    }

    // جلب التمارين لجميع الدروس بشكل متوازي لتحسين الأداء
    const lessonsWithExercises = await Promise.all(
      lessonsData.map(async (lesson) => {
        const exercises = await fetchExercisesForLesson(lesson);
        return mapLessonData(lesson, exercises);
      })
    );

    console.log(`تم جلب ${lessonsWithExercises.length} درس بنجاح`);
    return lessonsWithExercises;

  } catch (e) {
    console.error('خطأ غير متوقع في جلب الدروس:', e);
    return [];
  }
}

// جلب درس واحد مباشرة من Supabase
export async function fetchLessonFromSupabase(lessonId: string): Promise<Lesson | null> {
  try {
    const sanitizedLessonId = sanitizeIdForDatabase(lessonId);
    console.log(`جاري جلب الدرس ${sanitizedLessonId} مباشرة من Supabase...`);

    // جلب الدرس المحدد فقط
    const { data: lessonData, error: lessonError } = await supabase
      .from('lessons')
      .select('*')
      .eq('id', sanitizedLessonId)
      .single();

    if (lessonError) {
      console.error(`خطأ في جلب الدرس ${lessonId} من Supabase:`, lessonError);
      return null;
    }

    if (!lessonData) {
      console.log(`لم يتم العثور على الدرس ${lessonId}`);
      return null;
    }

    // جلب التمارين للدرس
    const exercises = await fetchExercisesForLesson(lessonData);
    const lesson = mapLessonData(lessonData, exercises);

    console.log(`تم جلب الدرس "${lesson.title}" مع ${lesson.exercises.length} تمارين بنجاح`);
    return lesson;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب الدرس ${lessonId}:`, e);
    return null;
  }
}

// جلب الدروس حسب المادة من Supabase
export async function fetchLessonsForSubjectFromSupabase(subjectId: string): Promise<Lesson[]> {
  try {
    const sanitizedSubjectId = sanitizeIdForDatabase(subjectId);
    // جلب الدروس المرتبطة بالمادة مرتبة حسب display_order
    const { data: lessonsData, error: lessonsError } = await supabase
      .from('lessons')
      .select('*')
      .eq('subject_id', sanitizedSubjectId)
      .order('display_order', { ascending: true });

    if (lessonsError) {
      console.error(`خطأ في جلب الدروس للمادة ${subjectId} من Supabase:`, lessonsError);
      return [];
    }

    if (!lessonsData || lessonsData.length === 0) {
      console.log(`لا توجد دروس للمادة ${subjectId}`);
      return [];
    }

    // جلب التمارين لجميع الدروس بشكل متوازي لتحسين الأداء
    const lessonsWithExercises = await Promise.all(
      lessonsData.map(async (lesson) => {
        const exercises = await fetchExercisesForLesson(lesson);
        return mapLessonData(lesson, exercises);
      })
    );

    console.log(`تم جلب ${lessonsWithExercises.length} درس للمادة ${subjectId} بنجاح`);
    return lessonsWithExercises;

  } catch (e) {
    console.error(`خطأ غير متوقع في جلب الدروس للمادة ${subjectId}:`, e);
    return [];
  }
}

// جلب جميع الامتحانات من Supabase
export async function fetchExamsFromSupabase(): Promise<Exam[]> {
  try {
    const { data, error } = await supabase
      .from('exams')
      .select('*');

    if (error) {
      console.error('خطأ في جلب الامتحانات من Supabase:', error);
      return [];
    }

    // Map database field names to match our type expectations with URL resolution
    const mappedExams = (data || []).map(exam => ({
      id: exam.id,
      lessonId: exam.lesson_id || '',
      hint: exam.hint || '',
      exerciseImageUrl: resolveFileUrl(exam.exercise_image_url) || '',
      solutionImageUrl: resolveFileUrl(exam.solution_image_url) || ''
    }));

    console.log(`تم جلب ${mappedExams.length} امتحان بنجاح`);
    return mappedExams;
  } catch (e) {
    console.error('خطأ غير متوقع في جلب الامتحانات:', e);
    return [];
  }
}

// جلب الامتحانات حسب الدرس من Supabase
export async function fetchExamsForLessonFromSupabase(lessonId: string): Promise<Exam[]> {
  try {
    const sanitizedLessonId = sanitizeIdForDatabase(lessonId);
    const { data, error } = await supabase
      .from('exams')
      .select('*')
      .eq('lesson_id', sanitizedLessonId);

    if (error) {
      console.error(`خطأ في جلب الامتحانات للدرس ${lessonId} من Supabase:`, error);
      return [];
    }

    // Map database field names to match our type expectations with URL resolution
    const mappedExams = (data || []).map(exam => ({
      id: exam.id,
      lessonId: exam.lesson_id || '',
      hint: exam.hint || '',
      exerciseImageUrl: resolveFileUrl(exam.exercise_image_url) || '',
      solutionImageUrl: resolveFileUrl(exam.solution_image_url) || ''
    }));

    console.log(`تم جلب ${mappedExams.length} امتحان للدرس ${lessonId} بنجاح`);
    return mappedExams;
  } catch (e) {
    console.error(`خطأ غير متوقع في جلب الامتحانات للدرس ${lessonId}:`, e);
    return [];
  }
}
