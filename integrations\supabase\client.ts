// Supabase Client Configuration
// يقرأ إعدادات Supabase من متغيرات البيئة
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// قراءة متغيرات البيئة مع القيم الافتراضية للبناء
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ckjjqlbzflnxolflixkq.supabase.co';
const SUPABASE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrampxbGJ6ZmxueG9sZmxpeGtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1MjE2MTQsImV4cCI6MjA2MzA5NzYxNH0.wx8p_RtQh-ETxsUIv2g6uL31Jadidyd7R2dbQENtgGk';

// التحقق من وجود المتغيرات المطلوبة في وقت التشغيل فقط
function validateEnvironmentVariables() {
  if (typeof window !== 'undefined') {
    // التحقق في المتصفح فقط
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      console.warn('متغير البيئة NEXT_PUBLIC_SUPABASE_URL مفقود. يتم استخدام القيمة الافتراضية.');
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('متغير البيئة NEXT_PUBLIC_SUPABASE_ANON_KEY مفقود. يتم استخدام القيمة الافتراضية.');
    }
  }
}

// تشغيل التحقق
validateEnvironmentVariables();

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
