# نظام العناوين المخصصة للتمارين - Custom Titles System

## 📋 ملخص النظام

تم تطوير نظام جديد يسمح بإضافة عناوين مخصصة للتمارين والدروس والفروض والامتحانات بدلاً من الاعتماد على العناوين المُولدة تلقائياً مثل "تمرين 1"، "تمرين 2"، إلخ.

## 🎯 المشكلة التي تم حلها

**قبل التحديث:**
- جميع التمارين تظهر بعناوين متكررة: "تمرين 1"، "تمرين 2"، "تمرين 3"
- لا يمكن تمييز محتوى التمارين من العنوان
- صعوبة في التنقل والبحث عن تمارين محددة

**بعد التحديث:**
- إمكانية إضافة عناوين مخصصة مثل: "حل المعادلات من الدرجة الثانية"، "قوانين نيوتن في الحركة"
- عناوين وصفية تساعد الطلاب على فهم محتوى التمرين
- تحسين تجربة المستخدم والتنقل

## 🔧 التحديثات التقنية

### 1. تحديث قاعدة البيانات
تم إضافة حقل `title` إلى الجداول التالية:
- `exercises` - للتمارين
- `homeworks` - للفروض
- `exams` - للامتحانات  
- `summaries` - للملخصات

### 2. تحديث أنواع TypeScript
```typescript
export interface Exercise {
  id: string;
  title?: string;        // ← حقل جديد
  question?: string;
  solution?: string;
  hint?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
}

export interface Homework {
  id: string;
  lessonId: string;
  title?: string;        // ← حقل جديد
  question?: string;
  solution?: string;
  hint?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
}

// نفس التحديث للـ Exam و Summary
```

### 3. تحديث مكونات العرض
تم تحديث المكونات التالية لدعم العناوين المخصصة:

#### أ. TableContentList
```typescript
// قبل التحديث
{lessonTitle ? `${lessonTitle} - ${labels.title} ${index + 1}` : `${labels.title} ${index + 1}`}

// بعد التحديث  
{exercise.title || (lessonTitle ? `${lessonTitle} - ${labels.title} ${index + 1}` : `${labels.title} ${index + 1}`)}
```

#### ب. ContentCard
```typescript
interface ContentCardProps {
  // ... خصائص أخرى
  customTitle?: string;  // ← خاصية جديدة
}

// في العرض
<h3>{customTitle || labels.title}</h3>
```

#### ج. جميع بطاقات المحتوى
- `ExerciseCard.tsx`
- `HomeworkCard.tsx` 
- `ExamCard.tsx`
- `SummaryCard.tsx`

## 📊 كيفية الاستخدام

### 1. إضافة عنوان مخصص لتمرين جديد
```sql
INSERT INTO exercises (
  id, 
  lesson_id, 
  title,                    -- ← العنوان المخصص
  exercise_image_url, 
  solution_image_url
) VALUES (
  'exercise_123',
  'lesson_456', 
  'حل المعادلات التربيعية',  -- ← عنوان وصفي
  'files/math/equation.pdf',
  'files/math/equation_solution.pdf'
);
```

### 2. تحديث عنوان تمرين موجود
```sql
UPDATE exercises 
SET title = 'قوانين الحركة - تطبيقات عملية'
WHERE id = 'exercise_123';
```

### 3. إزالة العنوان المخصص (العودة للافتراضي)
```sql
UPDATE exercises 
SET title = NULL
WHERE id = 'exercise_123';
```

## 🎨 أمثلة على العناوين المخصصة

### للرياضيات:
- "حل المعادلات من الدرجة الثانية"
- "تطبيقات على نظرية فيثاغورس"
- "حساب المساحات والحجوم"
- "الدوال الخطية والتربيعية"

### للفيزياء:
- "قوانين نيوتن في الحركة"
- "الكهرباء الساكنة والمتحركة"
- "الضوء والانعكاس والانكسار"
- "الطاقة الحركية والكامنة"

### للكيمياء:
- "التفاعلات الكيميائية الأساسية"
- "الجدول الدوري والعناصر"
- "الأحماض والقواعد والأملاح"
- "الكيمياء العضوية - المركبات"

## ✅ الفوائد المحققة

### للطلاب:
- **وضوح أكبر**: معرفة محتوى التمرين من العنوان
- **تنقل أسهل**: العثور على التمارين المطلوبة بسرعة
- **تنظيم أفضل**: فهم تسلسل المواضيع والمفاهيم

### للمعلمين:
- **إدارة محتوى محسنة**: تنظيم التمارين حسب المواضيع
- **مرونة في التسمية**: استخدام أسماء تعكس المنهج المحلي
- **تتبع أفضل**: معرفة أي تمارين تم حلها أو تحتاج مراجعة

### للموقع:
- **SEO محسن**: عناوين وصفية تحسن ترتيب البحث
- **تجربة مستخدم أفضل**: واجهة أكثر وضوحاً وتنظيماً
- **قابلية الوصول**: محتوى أكثر وصفية للقارئات الصوتية

## 🔄 التوافق مع النظام القديم

النظام الجديد متوافق تماماً مع البيانات الموجودة:

- **إذا كان للتمرين عنوان مخصص**: يتم عرض العنوان المخصص
- **إذا لم يكن للتمرين عنوان مخصص**: يتم عرض العنوان الافتراضي ("تمرين 1"، إلخ)

هذا يعني أن جميع التمارين الموجودة ستستمر في العمل بنفس الطريقة، ويمكن تحديثها تدريجياً بعناوين مخصصة.

## 🚀 الخطوات التالية

1. **إضافة حقل title إلى قاعدة البيانات** (مطلوب تنفيذ SQL)
2. **تحديث البيانات الموجودة** بعناوين مخصصة تدريجياً
3. **إنشاء واجهة إدارة** لتسهيل إضافة وتعديل العناوين
4. **إضافة ميزة البحث** بالعناوين المخصصة

## 📝 ملاحظات مهمة

- حقل `title` اختياري (`nullable`) لضمان التوافق
- النظام يعمل مع جميع أنواع المحتوى (تمارين، فروض، امتحانات، ملخصات)
- يمكن استخدام العناوين باللغة العربية أو أي لغة أخرى
- العناوين تظهر في جميع أجزاء الموقع (الجداول، البطاقات، القوائم)
