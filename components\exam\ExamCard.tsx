'use client'

import React from 'react';
import ContentCard from '@/components/ui/content-card';
import type { Exercise } from '@/data/types';

interface ExamCardProps {
  exercise: Exercise;
  index: number;
  lessonTitle?: string;
}

export default function ExamCard({ exercise, index, lessonTitle }: ExamCardProps) {
  return (
    <ContentCard
      exerciseImageUrl={exercise.exerciseImageUrl}
      solutionImageUrl={exercise.solutionImageUrl}
      index={index}
      contentType="exam"
      lessonTitle={lessonTitle}
    />
  );
}
