-- سكريبت تحديث بنية المرحلة الثانوية في Supabase
-- تحويل المرحلة الثانوية إلى ثلاثة أقسام: جذع مشترك، الأولى باك، الثانية باك

-- 1. حذف المستوى الثانوي القديم والسنوات المرتبطة به
DELETE FROM years WHERE level_id = 'high';
DELETE FROM levels WHERE id = 'high';

-- 2. إضافة المستويات الجديدة
INSERT INTO levels (id, name, description, years) VALUES
('trunk_common', 'جذع مشترك', 'السنة الأولى من التعليم الثانوي - جذع مشترك', ARRAY['trunk_common_year']),
('first_bac', 'الأولى باك', 'السنة الثانية من التعليم الثانوي مع التخصصات', ARRAY['first_bac_sciences', 'first_bac_literature', 'first_bac_economics']),
('second_bac', 'الثانية باك', 'السنة الثالثة من التعليم الثانوي مع التخصصات', ARRAY['second_bac_sciences', 'second_bac_literature', 'second_bac_economics']);

-- 3. إضافة السنوات الجديدة
INSERT INTO years (id, name, level_id, subjects) VALUES
-- جذع مشترك
('trunk_common_year', 'جذع مشترك', 'trunk_common', ARRAY[]::text[]),

-- الأولى باك
('first_bac_sciences', 'الأولى باك علوم', 'first_bac', ARRAY[]::text[]),
('first_bac_literature', 'الأولى باك آداب', 'first_bac', ARRAY[]::text[]),
('first_bac_economics', 'الأولى باك اقتصاد', 'first_bac', ARRAY[]::text[]),

-- الثانية باك
('second_bac_sciences', 'الثانية باك علوم', 'second_bac', ARRAY[]::text[]),
('second_bac_literature', 'الثانية باك آداب', 'second_bac', ARRAY[]::text[]),
('second_bac_economics', 'الثانية باك اقتصاد', 'second_bac', ARRAY[]::text[]);

-- 4. إضافة مواد دراسية نموذجية لجذع مشترك
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('trunk_math', 'الرياضيات', '📐', 'trunk_common_year', ARRAY[]::text[]),
('trunk_physics', 'الفيزياء', '⚛️', 'trunk_common_year', ARRAY[]::text[]),
('trunk_chemistry', 'الكيمياء', '🧪', 'trunk_common_year', ARRAY[]::text[]),
('trunk_biology', 'علوم الحياة والأرض', '🌱', 'trunk_common_year', ARRAY[]::text[]),
('trunk_arabic', 'اللغة العربية', '📚', 'trunk_common_year', ARRAY[]::text[]),
('trunk_french', 'اللغة الفرنسية', '🇫🇷', 'trunk_common_year', ARRAY[]::text[]),
('trunk_english', 'اللغة الإنجليزية', '🇺🇸', 'trunk_common_year', ARRAY[]::text[]),
('trunk_history', 'التاريخ والجغرافيا', '🌍', 'trunk_common_year', ARRAY[]::text[]),
('trunk_philosophy', 'الفلسفة', '🤔', 'trunk_common_year', ARRAY[]::text[]),
('trunk_islamic', 'التربية الإسلامية', '☪️', 'trunk_common_year', ARRAY[]::text[]);

-- 5. إضافة مواد دراسية للأولى باك علوم
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_bac_sci_math', 'الرياضيات', '📐', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_physics', 'الفيزياء', '⚛️', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_chemistry', 'الكيمياء', '🧪', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_biology', 'علوم الحياة والأرض', '🌱', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_arabic', 'اللغة العربية', '📚', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_philosophy', 'الفلسفة', '🤔', 'first_bac_sciences', ARRAY[]::text[]),
('first_bac_sci_islamic', 'التربية الإسلامية', '☪️', 'first_bac_sciences', ARRAY[]::text[]);

-- 6. إضافة مواد دراسية للأولى باك آداب
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_bac_lit_arabic', 'اللغة العربية', '📚', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_philosophy', 'الفلسفة', '🤔', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_islamic', 'التربية الإسلامية', '☪️', 'first_bac_literature', ARRAY[]::text[]),
('first_bac_lit_math', 'الرياضيات', '📐', 'first_bac_literature', ARRAY[]::text[]);

-- 7. إضافة مواد دراسية للأولى باك اقتصاد
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('first_bac_eco_economics', 'الاقتصاد والتدبير', '💼', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_accounting', 'المحاسبة', '📊', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_math', 'الرياضيات', '📐', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_arabic', 'اللغة العربية', '📚', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_french', 'اللغة الفرنسية', '🇫🇷', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_english', 'اللغة الإنجليزية', '🇺🇸', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_history', 'التاريخ والجغرافيا', '🌍', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_philosophy', 'الفلسفة', '🤔', 'first_bac_economics', ARRAY[]::text[]),
('first_bac_eco_islamic', 'التربية الإسلامية', '☪️', 'first_bac_economics', ARRAY[]::text[]);

-- 8. تحديث مصفوفة years في جدول levels
UPDATE levels SET years = ARRAY['trunk_common_year'] WHERE id = 'trunk_common';
UPDATE levels SET years = ARRAY['first_bac_sciences', 'first_bac_literature', 'first_bac_economics'] WHERE id = 'first_bac';
UPDATE levels SET years = ARRAY['second_bac_sciences', 'second_bac_literature', 'second_bac_economics'] WHERE id = 'second_bac';

-- 9. تحديث مصفوفة subjects في جدول years
UPDATE years SET subjects = ARRAY[
    'trunk_math', 'trunk_physics', 'trunk_chemistry', 'trunk_biology', 
    'trunk_arabic', 'trunk_french', 'trunk_english', 'trunk_history', 
    'trunk_philosophy', 'trunk_islamic'
] WHERE id = 'trunk_common_year';

UPDATE years SET subjects = ARRAY[
    'first_bac_sci_math', 'first_bac_sci_physics', 'first_bac_sci_chemistry', 
    'first_bac_sci_biology', 'first_bac_sci_arabic', 'first_bac_sci_french', 
    'first_bac_sci_english', 'first_bac_sci_philosophy', 'first_bac_sci_islamic'
] WHERE id = 'first_bac_sciences';

UPDATE years SET subjects = ARRAY[
    'first_bac_lit_arabic', 'first_bac_lit_french', 'first_bac_lit_english', 
    'first_bac_lit_history', 'first_bac_lit_philosophy', 'first_bac_lit_islamic', 
    'first_bac_lit_math'
] WHERE id = 'first_bac_literature';

UPDATE years SET subjects = ARRAY[
    'first_bac_eco_economics', 'first_bac_eco_accounting', 'first_bac_eco_math', 
    'first_bac_eco_arabic', 'first_bac_eco_french', 'first_bac_eco_english', 
    'first_bac_eco_history', 'first_bac_eco_philosophy', 'first_bac_eco_islamic'
] WHERE id = 'first_bac_economics';

-- 10. عرض النتائج للتأكد من التحديث
SELECT 'المستويات الجديدة:' as info;
SELECT id, name, description FROM levels WHERE id IN ('trunk_common', 'first_bac', 'second_bac');

SELECT 'السنوات الجديدة:' as info;
SELECT id, name, level_id FROM years WHERE level_id IN ('trunk_common', 'first_bac', 'second_bac');

SELECT 'المواد الدراسية الجديدة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN (
    'trunk_common_year', 'first_bac_sciences', 'first_bac_literature', 'first_bac_economics'
);
