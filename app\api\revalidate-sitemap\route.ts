import { NextRequest, NextResponse } from 'next/server'
import { revalidatePath } from 'next/cache'

// API route لإجبار تحديث sitemap.xml
export async function POST(request: NextRequest) {
  try {
    // التحقق من وجود مفتاح الأمان (اختياري)
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.REVALIDATE_TOKEN
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { error: 'غير مصرح' },
        { status: 401 }
      )
    }

    // إجبار تحديث sitemap.xml
    revalidatePath('/sitemap.xml')
    
    console.log('تم تحديث sitemap.xml بنجاح')
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث sitemap.xml بنجاح',
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('خطأ في تحديث sitemap.xml:', error)
    
    return NextResponse.json(
      { 
        error: 'فشل في تحديث sitemap.xml',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

// دعم GET أيضاً للاختبار
export async function GET() {
  return NextResponse.json({
    message: 'استخدم POST لتحديث sitemap.xml',
    usage: 'POST /api/revalidate-sitemap',
    headers: {
      'Authorization': 'Bearer YOUR_TOKEN (اختياري)'
    }
  })
}
