-- سكريبت تحديث بنية الثانية باك في Supabase
-- تحويل الثانية باك إلى التخصصات المغربية الفعلية

-- 1. حذف السنوات الحالية للثانية باك والمواد المرتبطة بها
DELETE FROM subjects WHERE year_id IN ('second_bac_sciences', 'second_bac_literature', 'second_bac_economics');
DELETE FROM years WHERE id IN ('second_bac_sciences', 'second_bac_literature', 'second_bac_economics');

-- 2. تحديث مستوى الثانية باك ليحتوي على التخصصات المغربية الجديدة
UPDATE levels
SET years = ARRAY['second_bac_math_a', 'second_bac_math_b', 'second_bac_physics', 'second_bac_biology', 'second_bac_agriculture', 'second_bac_tech_elec', 'second_bac_tech_mech', 'second_bac_economics', 'second_bac_accounting', 'second_bac_literature', 'second_bac_humanities'],
    description = 'السنة الثالثة من التعليم الثانوي مع التخصصات المغربية'
WHERE id = 'second_bac';

-- 3. إضافة السنوات الجديدة للتخصصات المغربية
INSERT INTO years (id, name, level_id, subjects) VALUES
('second_bac_math_a', 'الثانية بكالوريا - العلوم الرياضية أ', 'second_bac', ARRAY[]::text[]),
('second_bac_math_b', 'الثانية بكالوريا - العلوم الرياضية ب', 'second_bac', ARRAY[]::text[]),
('second_bac_physics', 'الثانية بكالوريا - العلوم الفيزيائية', 'second_bac', ARRAY[]::text[]),
('second_bac_biology', 'الثانية بكالوريا - علوم الحياة والأرض', 'second_bac', ARRAY[]::text[]),
('second_bac_agriculture', 'الثانية بكالوريا - العلوم الزراعية', 'second_bac', ARRAY[]::text[]),
('second_bac_tech_elec', 'الثانية بكالوريا - العلوم والتكنولوجيات الكهربائية', 'second_bac', ARRAY[]::text[]),
('second_bac_tech_mech', 'الثانية بكالوريا - العلوم والتكنولوجيات الميكانيكية', 'second_bac', ARRAY[]::text[]),
('second_bac_economics', 'الثانية بكالوريا - العلوم الاقتصادية', 'second_bac', ARRAY[]::text[]),
('second_bac_accounting', 'الثانية بكالوريا - علوم التدبير المحاسباتي', 'second_bac', ARRAY[]::text[]),
('second_bac_literature', 'الثانية بكالوريا - الآداب', 'second_bac', ARRAY[]::text[]),
('second_bac_humanities', 'الثانية بكالوريا - العلوم الإنسانية', 'second_bac', ARRAY[]::text[]);

-- 4. إضافة مواد العلوم الرياضية أ
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_math_a_math', 'الرياضيات', '📐', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_biology', 'علوم الحياة والأرض', '🌱', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_arabic', 'اللغة العربية', '📚', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_philosophy', 'الفلسفة', '🤔', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_islamic', 'التربية الإسلامية', '☪️', 'second_bac_math_a', ARRAY[]::text[]),
('second_math_a_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_math_a', ARRAY[]::text[]);

-- 5. إضافة مواد العلوم الرياضية ب
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_math_b_math', 'الرياضيات', '📐', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_engineering', 'علوم المهندس', '🔧', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_arabic', 'اللغة العربية', '📚', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_philosophy', 'الفلسفة', '🤔', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_islamic', 'التربية الإسلامية', '☪️', 'second_bac_math_b', ARRAY[]::text[]),
('second_math_b_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_math_b', ARRAY[]::text[]);

-- 6. إضافة مواد العلوم الفيزيائية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_physics_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_math', 'الرياضيات', '📐', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_biology', 'علوم الحياة والأرض', '🌱', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_arabic', 'اللغة العربية', '📚', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_philosophy', 'الفلسفة', '🤔', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_islamic', 'التربية الإسلامية', '☪️', 'second_bac_physics', ARRAY[]::text[]),
('second_physics_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_physics', ARRAY[]::text[]);

-- 7. إضافة مواد علوم الحياة والأرض
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_biology_biology', 'علوم الحياة والأرض', '🌱', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_math', 'الرياضيات', '📐', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_arabic', 'اللغة العربية', '📚', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_philosophy', 'الفلسفة', '🤔', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_islamic', 'التربية الإسلامية', '☪️', 'second_bac_biology', ARRAY[]::text[]),
('second_biology_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_biology', ARRAY[]::text[]);

-- 8. إضافة مواد العلوم الزراعية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_agri_agriculture', 'العلوم الزراعية', '🌾', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_biology', 'علوم الحياة والأرض', '🌱', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_math', 'الرياضيات', '📐', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_arabic', 'اللغة العربية', '📚', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_philosophy', 'الفلسفة', '🤔', 'second_bac_agriculture', ARRAY[]::text[]),
('second_agri_islamic', 'التربية الإسلامية', '☪️', 'second_bac_agriculture', ARRAY[]::text[]);

-- 9. إضافة مواد العلوم والتكنولوجيات الكهربائية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_elec_engineering', 'الهندسة الكهربائية', '⚡', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_math', 'الرياضيات', '📐', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_technology', 'التكنولوجيا الصناعية', '⚙️', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_arabic', 'اللغة العربية', '📚', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_philosophy', 'الفلسفة', '🤔', 'second_bac_tech_elec', ARRAY[]::text[]),
('second_elec_islamic', 'التربية الإسلامية', '☪️', 'second_bac_tech_elec', ARRAY[]::text[]);

-- 10. إضافة مواد العلوم والتكنولوجيات الميكانيكية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_mech_engineering', 'الهندسة الميكانيكية', '🔧', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_math', 'الرياضيات', '📐', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_physics', 'الفيزياء والكيمياء', '⚛️', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_technology', 'التكنولوجيا الصناعية', '⚙️', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_arabic', 'اللغة العربية', '📚', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_philosophy', 'الفلسفة', '🤔', 'second_bac_tech_mech', ARRAY[]::text[]),
('second_mech_islamic', 'التربية الإسلامية', '☪️', 'second_bac_tech_mech', ARRAY[]::text[]);

-- 11. إضافة مواد العلوم الاقتصادية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_econ_economics', 'الاقتصاد العام', '💼', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_math', 'الرياضيات', '📐', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_statistics', 'الإحصاء', '📊', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_arabic', 'اللغة العربية', '📚', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_philosophy', 'الفلسفة', '🤔', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_islamic', 'التربية الإسلامية', '☪️', 'second_bac_economics', ARRAY[]::text[]),
('second_econ_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_economics', ARRAY[]::text[]);

-- 12. إضافة مواد علوم التدبير المحاسباتي
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_acc_accounting', 'التدبير المحاسباتي', '📊', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_economics', 'الاقتصاد العام', '💼', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_math', 'الرياضيات', '📐', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_arabic', 'اللغة العربية', '📚', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_philosophy', 'الفلسفة', '🤔', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_islamic', 'التربية الإسلامية', '☪️', 'second_bac_accounting', ARRAY[]::text[]),
('second_acc_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_accounting', ARRAY[]::text[]);

-- 13. إضافة مواد الآداب
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_lit_arabic', 'اللغة العربية', '📚', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_philosophy', 'الفلسفة', '🤔', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_islamic', 'التربية الإسلامية', '☪️', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_literature', 'الأدب العربي', '📖', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_math', 'الرياضيات', '📐', 'second_bac_literature', ARRAY[]::text[]),
('second_lit_sociology', 'علم الاجتماع', '👥', 'second_bac_literature', ARRAY[]::text[]);

-- 14. إضافة مواد العلوم الإنسانية
INSERT INTO subjects (id, name, icon, year_id, lessons) VALUES
('second_hum_history', 'التاريخ والجغرافيا', '🌍', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_philosophy', 'الفلسفة', '🤔', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_arabic', 'اللغة العربية', '📚', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_french', 'اللغة الفرنسية', '🇫🇷', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_english', 'اللغة الإنجليزية', '🇺🇸', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_islamic', 'التربية الإسلامية', '☪️', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_sociology', 'علم الاجتماع', '👥', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_psychology', 'علم النفس', '🧠', 'second_bac_humanities', ARRAY[]::text[]),
('second_hum_math', 'الرياضيات', '📐', 'second_bac_humanities', ARRAY[]::text[]);

-- 15. تحديث مصفوفة subjects في جدول years
UPDATE years SET subjects = ARRAY[
    'second_math_a_math', 'second_math_a_physics', 'second_math_a_biology', 'second_math_a_arabic',
    'second_math_a_french', 'second_math_a_english', 'second_math_a_philosophy', 'second_math_a_islamic', 'second_math_a_history'
] WHERE id = 'second_bac_math_a';

UPDATE years SET subjects = ARRAY[
    'second_math_b_math', 'second_math_b_physics', 'second_math_b_engineering', 'second_math_b_arabic',
    'second_math_b_french', 'second_math_b_english', 'second_math_b_philosophy', 'second_math_b_islamic', 'second_math_b_history'
] WHERE id = 'second_bac_math_b';

UPDATE years SET subjects = ARRAY[
    'second_physics_physics', 'second_physics_math', 'second_physics_biology', 'second_physics_arabic',
    'second_physics_french', 'second_physics_english', 'second_physics_philosophy', 'second_physics_islamic', 'second_physics_history'
] WHERE id = 'second_bac_physics';

UPDATE years SET subjects = ARRAY[
    'second_biology_biology', 'second_biology_physics', 'second_biology_math', 'second_biology_arabic',
    'second_biology_french', 'second_biology_english', 'second_biology_philosophy', 'second_biology_islamic', 'second_biology_history'
] WHERE id = 'second_bac_biology';

UPDATE years SET subjects = ARRAY[
    'second_agri_agriculture', 'second_agri_biology', 'second_agri_physics', 'second_agri_math',
    'second_agri_arabic', 'second_agri_french', 'second_agri_english', 'second_agri_philosophy', 'second_agri_islamic'
] WHERE id = 'second_bac_agriculture';

UPDATE years SET subjects = ARRAY[
    'second_elec_engineering', 'second_elec_math', 'second_elec_physics', 'second_elec_technology',
    'second_elec_arabic', 'second_elec_french', 'second_elec_english', 'second_elec_philosophy', 'second_elec_islamic'
] WHERE id = 'second_bac_tech_elec';

UPDATE years SET subjects = ARRAY[
    'second_mech_engineering', 'second_mech_math', 'second_mech_physics', 'second_mech_technology',
    'second_mech_arabic', 'second_mech_french', 'second_mech_english', 'second_mech_philosophy', 'second_mech_islamic'
] WHERE id = 'second_bac_tech_mech';

UPDATE years SET subjects = ARRAY[
    'second_econ_economics', 'second_econ_math', 'second_econ_statistics', 'second_econ_arabic',
    'second_econ_french', 'second_econ_english', 'second_econ_philosophy', 'second_econ_islamic', 'second_econ_history'
] WHERE id = 'second_bac_economics';

UPDATE years SET subjects = ARRAY[
    'second_acc_accounting', 'second_acc_economics', 'second_acc_math', 'second_acc_arabic',
    'second_acc_french', 'second_acc_english', 'second_acc_philosophy', 'second_acc_islamic', 'second_acc_history'
] WHERE id = 'second_bac_accounting';

UPDATE years SET subjects = ARRAY[
    'second_lit_arabic', 'second_lit_french', 'second_lit_english', 'second_lit_history',
    'second_lit_philosophy', 'second_lit_islamic', 'second_lit_literature', 'second_lit_math', 'second_lit_sociology'
] WHERE id = 'second_bac_literature';

UPDATE years SET subjects = ARRAY[
    'second_hum_history', 'second_hum_philosophy', 'second_hum_arabic', 'second_hum_french',
    'second_hum_english', 'second_hum_islamic', 'second_hum_sociology', 'second_hum_psychology', 'second_hum_math'
] WHERE id = 'second_bac_humanities';

-- 16. عرض النتائج للتأكد من التحديث
SELECT 'المستوى المحدث:' as info;
SELECT id, name, description, array_length(years, 1) as years_count FROM levels WHERE id = 'second_bac';

SELECT 'السنوات الجديدة للثانية باك:' as info;
SELECT id, name, level_id FROM years WHERE level_id = 'second_bac' ORDER BY id;

SELECT 'المواد الدراسية الجديدة:' as info;
SELECT id, name, year_id FROM subjects WHERE year_id IN (
    'second_bac_math_a', 'second_bac_math_b', 'second_bac_physics', 'second_bac_biology', 'second_bac_agriculture',
    'second_bac_tech_elec', 'second_bac_tech_mech', 'second_bac_economics', 'second_bac_accounting',
    'second_bac_literature', 'second_bac_humanities'
) ORDER BY year_id, name;

-- 17. إحصائيات التحديث
SELECT
    'إحصائيات التحديث' as info,
    (SELECT COUNT(*) FROM years WHERE level_id = 'second_bac') as total_years,
    (SELECT COUNT(*) FROM subjects WHERE year_id IN (
        'second_bac_math_a', 'second_bac_math_b', 'second_bac_physics', 'second_bac_biology', 'second_bac_agriculture',
        'second_bac_tech_elec', 'second_bac_tech_mech', 'second_bac_economics', 'second_bac_accounting',
        'second_bac_literature', 'second_bac_humanities'
    )) as total_subjects;

-- 18. التحقق من البيانات النهائية
SELECT 'التحقق النهائي من البنية:' as final_check;

-- عرض المستوى المحدث
SELECT 'مستوى الثانية باك:' as section, id, name, description, array_length(years, 1) as years_count
FROM levels WHERE id = 'second_bac';

-- عرض السنوات الإحدى عشر
SELECT 'السنوات الإحدى عشر:' as section, id, name, level_id
FROM years WHERE level_id = 'second_bac' ORDER BY id;

-- عرض عدد المواد لكل تخصص
SELECT
    'عدد المواد لكل تخصص:' as section,
    year_id as specialization,
    COUNT(*) as subjects_count
FROM subjects
WHERE year_id IN (
    'second_bac_math_a', 'second_bac_math_b', 'second_bac_physics', 'second_bac_biology', 'second_bac_agriculture',
    'second_bac_tech_elec', 'second_bac_tech_mech', 'second_bac_economics', 'second_bac_accounting',
    'second_bac_literature', 'second_bac_humanities'
)
GROUP BY year_id
ORDER BY year_id;
