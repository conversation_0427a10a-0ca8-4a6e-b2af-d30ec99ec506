'use client';

import { useEffect, useRef, useState } from 'react';
import LessonBreadcrumb from './LessonBreadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from 'lucide-react';
import { useTheme } from 'next-themes';

interface LessonHeaderProps {
  title: string;
  description: string;
  subjectName: string;
  yearName: string;
  levelName: string;
  yearId: string;
  subjectId: string;
}

const LessonHeader = ({
  title,
  description,
  subjectName,
  yearName,
  levelName,
  yearId,
  subjectId
}: LessonHeaderProps) => {
  const { theme } = useTheme();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="mb-8">
      <LessonBreadcrumb
        yearId={yearId}
        yearName={yearName}
        subjectId={subjectId}
        subjectName={subjectName}
      />

      <h1 className="text-3xl font-bold text-primary mt-2">{title}</h1>

      <div ref={dropdownRef} className="relative mt-1">
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <DropdownMenuTrigger className="flex items-center gap-1 text-foreground hover:text-primary transition-colors">
            {subjectName} | {yearName} | {levelName}
            <ChevronDown className="h-4 w-4" />
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className={`${theme === 'dark' ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-foreground border-gray-200'}`}
            sideOffset={5}
            align="start"
          >
            <DropdownMenuItem className="cursor-pointer hover:bg-accent">
              {subjectName}
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer hover:bg-accent">
              {yearName}
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer hover:bg-accent">
              {levelName}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="mb-6 mt-6">
        <h2 className="text-2xl font-bold mb-2 text-foreground">{description}</h2>
        <div className="h-1 w-16 bg-primary my-4"></div>
      </div>
    </div>
  );
};

export default LessonHeader;
