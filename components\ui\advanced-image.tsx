'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface AdvancedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
  sizes?: string
  srcSet?: string
  loading?: 'lazy' | 'eager'
  decoding?: 'async' | 'sync' | 'auto'
}

interface ImageState {
  isLoaded: boolean
  isLoading: boolean
  hasError: boolean
  isInView: boolean
  loadStartTime: number
  loadEndTime?: number
}

export function AdvancedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 80,
  placeholder,
  onLoad,
  onError,
  sizes,
  srcSet,
  loading = 'lazy',
  decoding = 'async'
}: AdvancedImageProps) {
  const [state, setState] = useState<ImageState>({
    isLoaded: false,
    isLoading: false,
    hasError: false,
    isInView: priority,
    loadStartTime: 0
  })

  const imgRef = useRef<HTMLImageElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const retryCountRef = useRef(0)
  const maxRetries = 3

  // تحسين URL الصورة
  const optimizeImageUrl = useCallback((url: string, width?: number, height?: number, quality?: number) => {
    if (!url) return placeholder || ''

    // إذا كانت الصورة من Supabase
    if (url.includes('supabase.co')) {
      const params = new URLSearchParams()
      
      if (width) params.append('width', width.toString())
      if (height) params.append('height', height.toString())
      if (quality) params.append('quality', quality.toString())
      
      // تحويل إلى WebP للحجم الأصغر
      params.append('format', 'webp')
      
      const separator = url.includes('?') ? '&' : '?'
      return `${url}${separator}${params.toString()}`
    }

    return url
  }, [placeholder])

  // إنشاء srcSet محسن
  const generateSrcSet = useCallback((baseUrl: string, width?: number) => {
    if (!width || !baseUrl.includes('supabase.co')) return srcSet

    const sizes = [0.5, 1, 1.5, 2] // مضاعفات الحجم
    return sizes
      .map(multiplier => {
        const scaledWidth = Math.round(width * multiplier)
        const optimizedUrl = optimizeImageUrl(baseUrl, scaledWidth, undefined, quality)
        return `${optimizedUrl} ${scaledWidth}w`
      })
      .join(', ')
  }, [optimizeImageUrl, quality, srcSet])

  // إعداد Intersection Observer
  useEffect(() => {
    if (priority || state.isInView) return

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting) {
          setState(prev => ({ ...prev, isInView: true, loadStartTime: performance.now() }))
          observer.disconnect()
        }
      },
      {
        rootMargin: '50px', // تحميل قبل 50px من الظهور
        threshold: 0.1
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    observerRef.current = observer

    return () => {
      observer.disconnect()
    }
  }, [priority, state.isInView])

  // معالج تحميل الصورة
  const handleLoad = useCallback(() => {
    const loadEndTime = performance.now()
    const loadDuration = loadEndTime - state.loadStartTime

    setState(prev => ({
      ...prev,
      isLoaded: true,
      isLoading: false,
      loadEndTime
    }))

    // تسجيل أداء التحميل
    if (process.env.NODE_ENV === 'development') {
      console.log(`🖼️ تم تحميل الصورة: ${alt} في ${loadDuration.toFixed(2)}ms`)
    }

    onLoad?.()
  }, [alt, onLoad, state.loadStartTime])

  // معالج خطأ التحميل
  const handleError = useCallback(() => {
    retryCountRef.current += 1

    if (retryCountRef.current < maxRetries) {
      // إعادة المحاولة بعد تأخير
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          hasError: false,
          isLoading: true,
          loadStartTime: performance.now()
        }))
      }, 1000 * retryCountRef.current)
    } else {
      setState(prev => ({
        ...prev,
        hasError: true,
        isLoading: false
      }))

      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ فشل تحميل الصورة: ${alt} بعد ${maxRetries} محاولات`)
      }

      onError?.()
    }
  }, [alt, onError])

  // بدء التحميل عند الحاجة
  useEffect(() => {
    if (state.isInView && !state.isLoaded && !state.isLoading && !state.hasError) {
      setState(prev => ({
        ...prev,
        isLoading: true,
        loadStartTime: performance.now()
      }))
    }
  }, [state.isInView, state.isLoaded, state.isLoading, state.hasError])

  const optimizedSrc = optimizeImageUrl(src, width, height, quality)
  const optimizedSrcSet = generateSrcSet(src, width)

  // Placeholder محسن
  const defaultPlaceholder = `data:image/svg+xml;base64,${btoa(`
    <svg width="${width || 300}" height="${height || 200}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial" font-size="14" fill="#9ca3af" text-anchor="middle" dy=".3em">
        ${state.isLoading ? 'جاري التحميل...' : state.hasError ? 'خطأ في التحميل' : 'صورة'}
      </text>
    </svg>
  `)}`

  return (
    <div 
      ref={imgRef}
      className={cn("relative overflow-hidden", className)}
      style={{ width, height }}
    >
      {/* Placeholder */}
      {(!state.isLoaded || state.hasError) && (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={placeholder || defaultPlaceholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover blur-sm"
          aria-hidden="true"
        />
      )}

      {/* الصورة الفعلية */}
      {state.isInView && !state.hasError && (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={optimizedSrc}
          srcSet={optimizedSrcSet}
          sizes={sizes || `${width}px`}
          alt={alt}
          className={cn(
            "w-full h-full object-cover transition-opacity duration-300",
            state.isLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading={loading}
          decoding={decoding}
          width={width}
          height={height}
        />
      )}
      
      {/* مؤشر التحميل */}
      {state.isLoading && state.isInView && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-xs text-muted-foreground">
              {retryCountRef.current > 0 ? `محاولة ${retryCountRef.current + 1}` : 'جاري التحميل...'}
            </span>
          </div>
        </div>
      )}

      {/* رسالة الخطأ */}
      {state.hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/80">
          <div className="text-center">
            <div className="text-2xl mb-2">⚠️</div>
            <p className="text-sm text-muted-foreground">فشل تحميل الصورة</p>
            <button
              onClick={() => {
                retryCountRef.current = 0
                setState(prev => ({
                  ...prev,
                  hasError: false,
                  isLoading: true,
                  loadStartTime: performance.now()
                }))
              }}
              className="mt-2 px-3 py-1 bg-primary text-primary-foreground rounded text-xs hover:bg-primary/90"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      )}

      {/* معلومات الأداء في بيئة التطوير */}
      {process.env.NODE_ENV === 'development' && state.isLoaded && state.loadEndTime && (
        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          {(state.loadEndTime - state.loadStartTime).toFixed(0)}ms
        </div>
      )}
    </div>
  )
}

// مكون مبسط للصور المصغرة
export function ThumbnailImage({
  src,
  alt,
  className,
  size = 100
}: {
  src: string
  alt: string
  className?: string
  size?: number
}) {
  return (
    <AdvancedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("rounded-md", className)}
      quality={60} // جودة أقل للصور المصغرة
      loading="lazy"
    />
  )
}

// مكون للصور عالية الأولوية
export function HeroBannerImage({
  src,
  alt,
  className,
  width = 1200,
  height = 600
}: {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
}) {
  return (
    <AdvancedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={true}
      quality={90} // جودة عالية للصور المهمة
      loading="eager"
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
    />
  )
}
