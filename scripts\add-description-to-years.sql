-- إضافة حقل description إلى جدول years
-- Add description field to years table

-- 1. إضافة عمود description إلى جدول years
ALTER TABLE public.years 
ADD COLUMN IF NOT EXISTS description text;

-- 2. إضافة وصف افتراضي للسنوات الموجودة
UPDATE public.years 
SET description = CASE 
    WHEN name LIKE '%جذع مشترك%' THEN 'السنة الأولى من التعليم الثانوي - تأسيس قوي في جميع المواد الأساسية'
    WHEN name LIKE '%الأولى بكالوريا%' OR name LIKE '%أولى باك%' THEN 'السنة الثانية من التعليم الثانوي - بداية التخصص الأكاديمي'
    WHEN name LIKE '%الثانية بكالوريا%' OR name LIKE '%ثانية باك%' THEN 'السنة الأخيرة من التعليم الثانوي - التحضير لامتحان البكالوريا'
    WHEN name LIKE '%السادس ابتدائي%' THEN 'السنة الأخيرة من التعليم الابتدائي - التحضير للانتقال إلى الإعدادي'
    WHEN name LIKE '%الخامس ابتدائي%' THEN 'السنة الخامسة من التعليم الابتدائي - تعميق المفاهيم الأساسية'
    WHEN name LIKE '%الرابع ابتدائي%' THEN 'السنة الرابعة من التعليم الابتدائي - بناء المهارات الأساسية'
    WHEN name LIKE '%الثالث ابتدائي%' THEN 'السنة الثالثة من التعليم الابتدائي - تطوير مهارات القراءة والكتابة'
    WHEN name LIKE '%الثاني ابتدائي%' THEN 'السنة الثانية من التعليم الابتدائي - تعلم الأساسيات'
    WHEN name LIKE '%الأول ابتدائي%' THEN 'السنة الأولى من التعليم الابتدائي - بداية الرحلة التعليمية'
    WHEN name LIKE '%الثالث إعدادي%' THEN 'السنة الأخيرة من التعليم الإعدادي - التحضير لامتحان الإعدادية'
    WHEN name LIKE '%الثاني إعدادي%' THEN 'السنة الثانية من التعليم الإعدادي - تطوير المهارات المتقدمة'
    WHEN name LIKE '%الأول إعدادي%' THEN 'السنة الأولى من التعليم الإعدادي - الانتقال من الابتدائي'
    ELSE 'استكشف المواد الدراسية لهذه السنة'
END
WHERE description IS NULL OR description = '';

-- 3. التحقق من النتائج
SELECT 'تم إضافة وصف للسنوات بنجاح:' as info;
SELECT id, name, description 
FROM public.years 
ORDER BY name;

-- 4. إضافة فهرس للبحث السريع في الوصف (اختياري)
CREATE INDEX IF NOT EXISTS idx_years_description ON public.years USING gin(to_tsvector('arabic', description));

COMMENT ON COLUMN public.years.description IS 'وصف السنة الدراسية';
