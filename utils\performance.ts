// Performance optimization utilities

// Debounce function for search and input optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle function for scroll and resize events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Memoization utility for expensive calculations
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>()
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = fn(...args)
    cache.set(key, result)
    return result
  }) as T
}

// Lazy loading utility for components
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = React.lazy(importFunc)

  return (props: React.ComponentProps<T>) =>
    React.createElement(
      React.Suspense,
      { fallback: fallback ? React.createElement(fallback) : React.createElement('div', {}, 'Loading...') },
      React.createElement(LazyComponent, props)
    )
}

// Image preloader utility
export function preloadImages(urls: string[]): Promise<void[]> {
  return Promise.all(
    urls.map(url => 
      new Promise<void>((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = reject
        img.src = url
      })
    )
  )
}

// Bundle size analyzer utility
export function analyzeBundleSize() {
  if (typeof window === 'undefined') return null
  
  const scripts = Array.from(document.querySelectorAll('script[src]'))
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
  
  return {
    scripts: scripts.length,
    styles: styles.length,
    totalResources: scripts.length + styles.length
  }
}

// Memory usage monitor
export function getMemoryUsage() {
  if (typeof window === 'undefined' || !(performance as any).memory) {
    return null
  }
  
  const memory = (performance as any).memory
  return {
    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
  }
}

// Performance timing utilities
export function measurePerformance<T>(
  name: string,
  fn: () => T
): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  
  console.log(`⏱️ ${name}: ${Math.round(end - start)}ms`)
  return result
}

export async function measureAsyncPerformance<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  
  console.log(`⏱️ ${name}: ${Math.round(end - start)}ms`)
  return result
}

// Web Vitals measurement
export function measureWebVitals() {
  if (typeof window === 'undefined') return
  
  // First Contentful Paint
  const paintEntries = performance.getEntriesByType('paint')
  const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
  
  // Navigation timing
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  
  return {
    fcp: fcp?.startTime,
    ttfb: navigation ? navigation.responseStart - navigation.requestStart : null,
    domLoad: navigation ? navigation.domContentLoadedEventEnd - navigation.fetchStart : null,
    windowLoad: navigation ? navigation.loadEventEnd - navigation.fetchStart : null
  }
}

// Resource loading optimization
export function optimizeResourceLoading() {
  if (typeof window === 'undefined') return
  
  // Preload critical resources
  const preloadLink = (href: string, as: string) => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    document.head.appendChild(link)
  }
  
  // Prefetch next page resources
  const prefetchLink = (href: string) => {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href
    document.head.appendChild(link)
  }
  
  return { preloadLink, prefetchLink }
}

// Cache management utilities
export class PerformanceCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  clear() {
    this.cache.clear()
  }
  
  size() {
    return this.cache.size
  }
  
  cleanup() {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.cache.delete(key))
  }
}

// Global performance cache instance
export const performanceCache = new PerformanceCache()

// Auto cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => performanceCache.cleanup(), 5 * 60 * 1000)
}

// React import (needed for lazy component utility)
import React from 'react'

export default {
  debounce,
  throttle,
  memoize,
  createLazyComponent,
  preloadImages,
  analyzeBundleSize,
  getMemoryUsage,
  measurePerformance,
  measureAsyncPerformance,
  measureWebVitals,
  optimizeResourceLoading,
  PerformanceCache,
  performanceCache
}
