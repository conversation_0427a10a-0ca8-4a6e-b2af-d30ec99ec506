'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { BookOpen, FileText, Pencil } from 'lucide-react';
import type { Lesson } from '@/data/types';
import LessonCard from './LessonCard';

interface SubjectTabsProps {
  lessons: Lesson[];
}

const SubjectTabs = ({ lessons }: SubjectTabsProps) => {
  // Categorize lessons by content type
  const exercises = lessons.filter(lesson => lesson.content_type === 'exercise');
  const homeworks = lessons.filter(lesson => lesson.content_type === 'homework');
  const summaries = lessons.filter(lesson => lesson.content_type === 'summary');

  console.log('Lessons data:', lessons);
  console.log('Exercises:', exercises);
  console.log('Homeworks:', homeworks);
  console.log('Summaries:', summaries);

  return (
    <Tabs defaultValue="exercises" className="w-full mb-8">
      <TabsList className="flex justify-center mb-6 w-full">
        <TabsTrigger value="exercises" className="flex items-center gap-2">
          <Pencil className="h-4 w-4" />
          <span>التمارين</span>
          <span className="bg-primary/10 text-primary rounded-full px-2 text-xs">
            {exercises.length}
          </span>
        </TabsTrigger>
        <TabsTrigger value="homeworks" className="flex items-center gap-2">
          <BookOpen className="h-4 w-4" />
          <span>الفروض</span>
          <span className="bg-primary/10 text-primary rounded-full px-2 text-xs">
            {homeworks.length}
          </span>
        </TabsTrigger>
        <TabsTrigger value="summaries" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          <span>ملخص الدروس</span>
          <span className="bg-primary/10 text-primary rounded-full px-2 text-xs">
            {summaries.length}
          </span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="exercises" className="space-y-6">
        {exercises.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {exercises.map(lesson => (
              <div key={lesson.id}>
                <LessonCard lesson={lesson} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">لا توجد تمارين متاحة حاليًا</p>
          </div>
        )}
      </TabsContent>

      <TabsContent value="homeworks" className="space-y-6">
        {homeworks.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {homeworks.map(lesson => (
              <div key={lesson.id}>
                <LessonCard lesson={lesson} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">لا توجد فروض متاحة حاليًا</p>
          </div>
        )}
      </TabsContent>

      <TabsContent value="summaries" className="space-y-6">
        {summaries.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {summaries.map(lesson => (
              <div key={lesson.id}>
                <LessonCard lesson={lesson} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">لا توجد ملخصات دروس متاحة حاليًا</p>
          </div>
        )}
      </TabsContent>
    </Tabs>
  );
};

export default SubjectTabs;
