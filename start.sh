#!/bin/bash

# Arab Education Exercises - Production Startup Script
# سكريبت بدء تشغيل المشروع للإنتاج

echo "🚀 بدء تشغيل مشروع التمارين التعليمية العربية..."

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً."
    exit 1
fi

# التحقق من إصدار Node.js
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.17.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "⚠️  تحذير: إصدار Node.js الحالي ($NODE_VERSION) قد لا يكون متوافقاً. الإصدار المطلوب: $REQUIRED_VERSION أو أحدث"
fi

# التحقق من وجود ملف package.json
if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود في المجلد الحالي."
    exit 1
fi

# التحقق من وجود ملف server.js
if [ ! -f "server.js" ]; then
    echo "❌ ملف server.js غير موجود."
    exit 1
fi

# إنشاء مجلد السجلات إذا لم يكن موجوداً
mkdir -p logs

# التحقق من متغيرات البيئة
if [ ! -f ".env.local" ] && [ ! -f ".env" ]; then
    echo "⚠️  تحذير: لم يتم العثور على ملف متغيرات البيئة (.env.local أو .env)"
    echo "📝 يرجى إنشاء ملف .env.local وإضافة متغيرات Supabase"
fi

# تثبيت التبعيات إذا لم تكن مثبتة
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت التبعيات..."
    npm install --production
    
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات."
        exit 1
    fi
fi

# التحقق من وجود مجلد البناء
if [ ! -d ".next" ]; then
    echo "🔨 بناء المشروع للإنتاج..."
    npm run build
    
    if [ $? -ne 0 ]; then
        echo "❌ فشل في بناء المشروع."
        exit 1
    fi
fi

# تعيين متغيرات البيئة
export NODE_ENV=production
export PORT=${PORT:-3000}

echo "✅ جميع الفحوصات تمت بنجاح!"
echo "🌍 البيئة: $NODE_ENV"
echo "🔌 المنفذ: $PORT"
echo ""

# بدء التطبيق
echo "🚀 بدء تشغيل الخادم..."
node server.js
