import {
  getLesson,
  getSubject,
  getYear,
  getLevel,
  getExercisesForLesson
} from '@/data/educationData';
import type { Lesson, Exercise } from '@/data/types';

interface LessonData {
  lesson: null | (Omit<Lesson, 'exercises'> & { exercises: Exercise[] });
  subjectName: string;
  yearName: string;
  levelName: string;
  subjectId: string;
  yearId: string;
}

export const findLessonById = async (lessonId: string): Promise<LessonData> => {
  const result: LessonData = {
    lesson: null,
    subjectName: '',
    yearName: '',
    levelName: '',
    subjectId: '',
    yearId: '',
  };

  const lesson = await getLesson(lessonId);

  if (lesson) {
    const subject = await getSubject(lesson.subjectId);

    if (subject) {
      const year = await getYear(subject.yearId);

      if (year) {
        const level = await getLevel(year.levelId);

        if (level) {
          // Get the actual exercise objects instead of just IDs
          const exerciseObjects = await getExercisesForLesson(lessonId);

          // Use type omission and intersection to correctly type the lesson with exercises
          result.lesson = {
            ...lesson,
            exercises: exerciseObjects
          };
          result.subjectName = subject.name;
          result.subjectId = subject.id;
          result.yearName = year.name;
          result.yearId = year.id;
          result.levelName = level.name;
        }
      }
    }
  }

  return result;
};
