# دليل التكوين المركزي للمشروع
# Central Configuration Guide

## 🎯 الهدف من هذا النظام

تم إنشاء نظام تكوين مركزي يسمح بتغيير رابط الموقع من مكان واحد فقط، بدلاً من تعديل كل ملف على حدة.

## 📁 الملفات المهمة

### 1. ملف التكوين المركزي
- **المسار:** `lib/config.ts`
- **الوظيفة:** يحتوي على جميع إعدادات المشروع
- **المميزات:**
  - قراءة تلقائية من متغيرات البيئة
  - قيم افتراضية آمنة
  - دوال مساعدة لبناء الروابط

### 2. ملف متغيرات البيئة
- **المسار:** `.env.example` (للمثال) و `.env.local` (للاستخدام الفعلي)
- **المتغير الرئيسي:** `NEXT_PUBLIC_SITE_URL`

## 🔧 كيفية تغيير رابط الموقع

### الطريقة الجديدة (المُحسنة):
1. انسخ ملف `.env.example` إلى `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. افتح ملف `.env.local` وغيّر قيمة `NEXT_PUBLIC_SITE_URL`:
   ```env
   NEXT_PUBLIC_SITE_URL=https://your-new-domain.com
   ```

3. أعد تشغيل المشروع:
   ```bash
   npm run dev
   ```

**هذا كل شيء!** 🎉 سيتم تطبيق التغيير على جميع أجزاء المشروع تلقائياً.

## 📋 الملفات التي تم تحديثها

### ملفات تستخدم التكوين المركزي الآن:
- ✅ `app/layout.tsx` - البيانات الوصفية الأساسية
- ✅ `app/sitemap.ts` - خريطة الموقع
- ✅ `app/robots.ts` - ملف robots.txt الديناميكي
- ✅ `lib/seo.ts` - مكتبة SEO
- ✅ `components/Breadcrumb.tsx` - مسارات التنقل
- ✅ `seo.config.js` - إعدادات SEO
- ✅ `next-sitemap.config.js` - تكوين خريطة الموقع

### ملفات تقرأ من متغير البيئة مباشرة:
- ✅ `next-sitemap.config.js`
- ✅ جميع ملفات التكوين الأخرى

## 🚀 المميزات الجديدة

### 1. إدارة مركزية
- تغيير واحد يؤثر على كامل المشروع
- لا حاجة لتذكر جميع الملفات
- تقليل الأخطاء البشرية

### 2. أمان أفضل
- قيم افتراضية آمنة
- التحقق من وجود متغيرات البيئة
- منع الأخطاء في حالة عدم وجود المتغيرات

### 3. سهولة الصيانة
- كود أكثر تنظيماً
- سهولة إضافة إعدادات جديدة
- توثيق واضح لكل إعداد

## 🔍 دوال مساعدة متاحة

```typescript
import { siteConfig, buildUrl, buildOgImageUrl, buildSitemapUrl } from '@/lib/config'

// الحصول على الرابط الأساسي
const baseUrl = siteConfig.url

// بناء رابط صفحة
const pageUrl = buildUrl('/about')

// بناء رابط صورة Open Graph
const ogImage = buildOgImageUrl('/custom-og.png')

// بناء رابط خريطة الموقع
const sitemapUrl = buildSitemapUrl()
```

## 📝 ملاحظات مهمة

### للمطورين:
- استخدم دائماً `siteConfig.url` بدلاً من كتابة الرابط مباشرة
- استخدم الدوال المساعدة لبناء الروابط
- تأكد من وجود قيم افتراضية للمتغيرات

### للنشر:
- تأكد من تحديث متغيرات البيئة في منصة النشر
- اختبر جميع الروابط بعد تغيير الدومين
- تحقق من عمل خريطة الموقع وملف robots.txt

## 🛠️ استكشاف الأخطاء

### إذا لم تظهر التغييرات:
1. تأكد من إعادة تشغيل الخادم
2. تحقق من وجود ملف `.env.local`
3. تأكد من صحة قيمة `NEXT_PUBLIC_SITE_URL`
4. امسح cache المتصفح

### إذا ظهرت أخطاء:
1. تحقق من صحة صيغة الرابط (يجب أن يبدأ بـ http:// أو https://)
2. تأكد من عدم وجود مسافات إضافية
3. تحقق من logs الخادم للحصول على تفاصيل الخطأ

## 📞 الدعم

إذا واجهت أي مشاكل، يرجى:
1. مراجعة هذا الدليل أولاً
2. التحقق من ملفات logs
3. التواصل مع فريق التطوير

---

**تم إنشاء هذا النظام لتسهيل إدارة المشروع وتقليل الأخطاء. استمتع بالتطوير! 🚀**
