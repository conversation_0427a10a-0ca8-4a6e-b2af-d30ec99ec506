-- سكريبت تحديث ترتيب المستويات التعليمية في Supabase
-- لجعل جذع مشترك يظهر أولاً، ثم الأولى باك، ثم الثانية باك

-- 1. إض<PERSON><PERSON>ة عمود للترتيب إذا لم يكن موجوداً
ALTER TABLE levels ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;

-- 2. تحديث ترتيب العرض للمستويات الثانوية فقط
-- ترك المرحلة الابتدائية والمتوسطة كما هي
UPDATE levels SET display_order = 1 WHERE id = 'primary';
UPDATE levels SET display_order = 2 WHERE id = 'middle';
UPDATE levels SET display_order = 3 WHERE id = 'trunk_common';
UPDATE levels SET display_order = 4 WHERE id = 'first_bac';
UPDATE levels SET display_order = 5 WHERE id = 'second_bac';

-- 3. التحقق من النتائج
SELECT 'ترتيب المستويات الجديد:' as info;
SELECT id, name, display_order
FROM levels
ORDER BY display_order;

-- 4. إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS levels_display_order_idx ON levels(display_order);

-- ملاحظة: بعد تنفيذ هذا السكريبت، ستحتاج إلى تحديث الكود ليستخدم display_order في الترتيب
