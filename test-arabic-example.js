// مثال سريع لاختبار فك ترميز الأسماء العربية
// يمكن تشغيله في وحدة التحكم للاختبار السريع

// محاكاة دالة فك الترميز
function decodeArabicFilename(encodedFilename) {
  try {
    // First try standard decodeURIComponent
    return decodeURIComponent(encodedFilename);
  } catch (error) {
    console.warn('Standard decoding failed, trying manual Arabic decode:', error);
    
    try {
      // Manual decode for common Arabic URL encoding patterns
      let decoded = encodedFilename
        // Basic Arabic letters
        .replace(/%D8%A7/g, 'ا')  // ا (alif)
        .replace(/%D8%A8/g, 'ب')  // ب (ba)
        .replace(/%D8%AA/g, 'ت')  // ت (ta)
        .replace(/%D8%AB/g, 'ث')  // ث (tha)
        .replace(/%D8%AC/g, 'ج')  // ج (jim)
        .replace(/%D8%AD/g, 'ح')  // ح (ha)
        .replace(/%D8%AE/g, 'خ')  // خ (kha)
        .replace(/%D8%AF/g, 'د')  // د (dal)
        .replace(/%D8%B0/g, 'ذ')  // ذ (dhal)
        .replace(/%D8%B1/g, 'ر')  // ر (ra)
        .replace(/%D8%B2/g, 'ز')  // ز (zay)
        .replace(/%D8%B3/g, 'س')  // س (sin)
        .replace(/%D8%B4/g, 'ش')  // ش (shin)
        .replace(/%D8%B5/g, 'ص')  // ص (sad)
        .replace(/%D8%B6/g, 'ض')  // ض (dad)
        .replace(/%D8%B7/g, 'ط')  // ط (ta)
        .replace(/%D8%B8/g, 'ظ')  // ظ (za)
        .replace(/%D8%B9/g, 'ع')  // ع (ain)
        .replace(/%D8%BA/g, 'غ')  // غ (ghain)
        .replace(/%D9%81/g, 'ف')  // ف (fa)
        .replace(/%D9%82/g, 'ق')  // ق (qaf)
        .replace(/%D9%83/g, 'ك')  // ك (kaf)
        .replace(/%D9%84/g, 'ل')  // ل (lam)
        .replace(/%D9%85/g, 'م')  // م (mim)
        .replace(/%D9%86/g, 'ن')  // ن (nun)
        .replace(/%D9%87/g, 'ه')  // ه (ha)
        .replace(/%D9%88/g, 'و')  // و (waw)
        .replace(/%D9%8A/g, 'ي')  // ي (ya)
        .replace(/%D9%89/g, 'ى')  // ى (alif maksura)
        .replace(/%D8%A9/g, 'ة')  // ة (ta marbuta)
        .replace(/%D8%A1/g, 'ء')  // ء (hamza)
        .replace(/%D8%A2/g, 'آ')  // آ (alif with madda)
        .replace(/%D8%A3/g, 'أ')  // أ (alif with hamza above)
        .replace(/%D8%A4/g, 'ؤ')  // ؤ (waw with hamza)
        .replace(/%D8%A5/g, 'إ')  // إ (alif with hamza below)
        .replace(/%D8%A6/g, 'ئ')  // ئ (ya with hamza)
        .replace(/%20/g, ' ')     // space
        .replace(/%2D/g, '-')     // dash
        .replace(/%2E/g, '.')     // dot
        .replace(/%28/g, '(')     // (
        .replace(/%29/g, ')')     // )
        .replace(/%5F/g, '_');    // underscore
      
      return decoded;
    } catch (manualError) {
      console.warn('Manual Arabic decode also failed:', manualError);
      return encodedFilename; // Return original if all attempts fail
    }
  }
}

// اختبار الأمثلة
console.log('=== اختبار فك ترميز الأسماء العربية ===');

// مثال 1: الرابط الأصلي من المشكلة
const example1 = 'Talamidi.com_اجتماعيات-جهة%20طنجة%20تطوان-10.pdf';
console.log('المثال 1 (أصلي):', example1);
console.log('المثال 1 (مفكوك):', decodeArabicFilename(example1));

// مثال 2: اسم ملف مُرمز بالكامل
const example2 = '%D8%A7%D8%AC%D8%AA%D9%85%D8%A7%D8%B9%D9%8A%D8%A7%D8%AA.pdf';
console.log('\nالمثال 2 (مُرمز):', example2);
console.log('المثال 2 (مفكوك):', decodeArabicFilename(example2));

// مثال 3: اسم ملف مختلط
const example3 = 'امتحان_%D8%A7%D9%84%D8%B1%D9%8A%D8%A7%D8%B6%D9%8A%D8%A7%D8%AA_2024.pdf';
console.log('\nالمثال 3 (مختلط):', example3);
console.log('المثال 3 (مفكوك):', decodeArabicFilename(example3));

// مثال 4: اختبار decodeURIComponent العادي
const example4 = encodeURIComponent('اجتماعيات-جهة طنجة تطوان.pdf');
console.log('\nالمثال 4 (مُرمز بـ encodeURIComponent):', example4);
console.log('المثال 4 (مفكوك):', decodeArabicFilename(example4));

console.log('\n=== انتهى الاختبار ===');

// تصدير الدالة للاستخدام
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { decodeArabicFilename };
}
