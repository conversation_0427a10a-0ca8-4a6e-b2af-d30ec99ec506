-- بيانات تجريبية للعناوين المخصصة
-- Sample data for custom titles

-- ===== تحديث التمارين الموجودة بعناوين مخصصة =====
-- Update existing exercises with custom titles

-- مثال: تحديث تمارين الرياضيات
-- Example: Update math exercises
/*
UPDATE public.exercises 
SET title = 'حل المعادلات من الدرجة الأولى'
WHERE lesson_id = 'math_lesson_1' AND id = 'exercise_1';

UPDATE public.exercises 
SET title = 'تطبيقات على المعادلات الخطية'
WHERE lesson_id = 'math_lesson_1' AND id = 'exercise_2';

UPDATE public.exercises 
SET title = 'مسائل متنوعة على الجبر'
WHERE lesson_id = 'math_lesson_1' AND id = 'exercise_3';
*/

-- ===== أمثلة على عناوين مخصصة للمواد المختلفة =====
-- Examples of custom titles for different subjects

-- 📐 الرياضيات - Mathematics
/*
-- الجبر
'حل المعادلات من الدرجة الثانية'
'تطبيقات على نظرية فيثاغورس'
'حساب المساحات والحجوم'
'الدوال الخطية والتربيعية'
'المتتاليات العددية والهندسية'

-- الهندسة
'خصائص المثلثات والمضلعات'
'الدائرة ومعادلاتها'
'التحويلات الهندسية'
'حساب الزوايا والأطوال'
'البرهان الهندسي'
*/

-- ⚗️ الفيزياء - Physics
/*
'قوانين نيوتن في الحركة'
'الكهرباء الساكنة والمتحركة'
'الضوء والانعكاس والانكسار'
'الطاقة الحركية والكامنة'
'الموجات الصوتية والضوئية'
'المغناطيسية والكهرومغناطيسية'
'الحرارة ودرجة الحرارة'
'الضغط والكثافة'
*/

-- 🧪 الكيمياء - Chemistry
/*
'التفاعلات الكيميائية الأساسية'
'الجدول الدوري والعناصر'
'الأحماض والقواعد والأملاح'
'الكيمياء العضوية - المركبات'
'حساب الكتل الذرية والجزيئية'
'التوازن الكيميائي'
'الأكسدة والاختزال'
'الكيمياء الحرارية'
*/

-- 🌱 علوم الحياة والأرض - Life and Earth Sciences
/*
'التنفس الخلوي والتمثيل الضوئي'
'الوراثة وقوانين مندل'
'النظم البيئية والتوازن البيولوجي'
'جهاز الدوران والتنفس'
'الجهاز العصبي والحواس'
'التكاثر والنمو'
'علم الأرض والصفائح التكتونية'
'المناخ والطقس'
*/

-- 📚 اللغة العربية - Arabic Language
/*
'قواعد النحو والإعراب'
'البلاغة والأساليب البيانية'
'الشعر العربي والعروض'
'النثر والمقالة'
'الإملاء وعلامات الترقيم'
'التعبير الكتابي والشفهي'
'الأدب الجاهلي والإسلامي'
'النقد الأدبي'
*/

-- 🇫🇷 اللغة الفرنسية - French Language
/*
'La grammaire française - القواعد الفرنسية'
'La conjugaison des verbes - تصريف الأفعال'
'Le vocabulaire thématique - المفردات الموضوعية'
'La compréhension écrite - الفهم المكتوب'
'L''expression orale - التعبير الشفهي'
'La littérature française - الأدب الفرنسي'
'La phonétique - علم الأصوات'
'La culture française - الثقافة الفرنسية'
*/

-- 🇬🇧 اللغة الإنجليزية - English Language
/*
'English Grammar Basics - أساسيات القواعد الإنجليزية'
'Vocabulary Building - بناء المفردات'
'Reading Comprehension - فهم المقروء'
'Writing Skills - مهارات الكتابة'
'Speaking and Pronunciation - التحدث والنطق'
'English Literature - الأدب الإنجليزي'
'Business English - الإنجليزية التجارية'
'Academic English - الإنجليزية الأكاديمية'
*/

-- 🏛️ التاريخ والجغرافيا - History and Geography
/*
-- التاريخ
'الحضارات القديمة'
'التاريخ الإسلامي والعربي'
'تاريخ المغرب والمغرب العربي'
'الحربان العالميتان'
'حركات التحرر والاستقلال'
'التاريخ المعاصر'

-- الجغرافيا
'الجغرافيا الطبيعية والمناخ'
'الجغرافيا البشرية والسكان'
'الموارد الطبيعية والاقتصاد'
'جغرافية المغرب'
'الجغرافيا الحضرية والريفية'
'البيئة والتنمية المستدامة'
*/

-- 🤔 الفلسفة - Philosophy
/*
'مدخل إلى الفلسفة'
'فلسفة الأخلاق والقيم'
'نظرية المعرفة'
'الفلسفة السياسية'
'فلسفة العلوم'
'الفلسفة الإسلامية'
'الفلسفة المعاصرة'
'المنطق والاستدلال'
*/

-- ===== سكريبت لإضافة عناوين تجريبية =====
-- Script to add sample titles

-- دالة لإضافة عناوين تجريبية للتمارين
CREATE OR REPLACE FUNCTION add_sample_exercise_titles()
RETURNS void AS $$
DECLARE
    sample_titles TEXT[] := ARRAY[
        'حل المعادلات من الدرجة الأولى',
        'تطبيقات على نظرية فيثاغورس',
        'حساب المساحات والحجوم',
        'قوانين نيوتن في الحركة',
        'التفاعلات الكيميائية الأساسية',
        'قواعد النحو والإعراب',
        'الحضارات القديمة',
        'مدخل إلى الفلسفة'
    ];
    exercise_record RECORD;
    title_index INTEGER := 1;
BEGIN
    -- تحديث أول 8 تمارين بعناوين تجريبية
    FOR exercise_record IN 
        SELECT id FROM public.exercises 
        WHERE title IS NULL 
        LIMIT 8
    LOOP
        UPDATE public.exercises 
        SET title = sample_titles[title_index]
        WHERE id = exercise_record.id;
        
        title_index := title_index + 1;
        
        RAISE NOTICE 'تم تحديث التمرين % بالعنوان: %', exercise_record.id, sample_titles[title_index-1];
    END LOOP;
    
    RAISE NOTICE '✅ تم إضافة % عنوان تجريبي للتمارين', title_index - 1;
END;
$$ LANGUAGE plpgsql;

-- دالة لإضافة عناوين تجريبية للفروض
CREATE OR REPLACE FUNCTION add_sample_homework_titles()
RETURNS void AS $$
DECLARE
    sample_titles TEXT[] := ARRAY[
        'فرض في الجبر والمعادلات',
        'فرض في الفيزياء - الحركة',
        'فرض في الكيمياء العامة',
        'فرض في قواعد اللغة العربية',
        'فرض في التاريخ المعاصر'
    ];
    homework_record RECORD;
    title_index INTEGER := 1;
BEGIN
    FOR homework_record IN 
        SELECT id FROM public.homeworks 
        WHERE title IS NULL 
        LIMIT 5
    LOOP
        UPDATE public.homeworks 
        SET title = sample_titles[title_index]
        WHERE id = homework_record.id;
        
        title_index := title_index + 1;
        
        RAISE NOTICE 'تم تحديث الفرض % بالعنوان: %', homework_record.id, sample_titles[title_index-1];
    END LOOP;
    
    RAISE NOTICE '✅ تم إضافة % عنوان تجريبي للفروض', title_index - 1;
END;
$$ LANGUAGE plpgsql;

-- ===== تشغيل الدوال لإضافة البيانات التجريبية =====
-- Run functions to add sample data

-- تشغيل دالة إضافة عناوين التمارين التجريبية
-- SELECT add_sample_exercise_titles();

-- تشغيل دالة إضافة عناوين الفروض التجريبية  
-- SELECT add_sample_homework_titles();

-- عرض النتائج
-- SELECT * FROM get_custom_titles_stats();

-- ===== ملاحظات مهمة =====
-- Important Notes

/*
🔧 لتطبيق هذا السكريبت:

1. قم بتشغيل السكريبت الأساسي أولاً:
   scripts/add-title-field-to-content-tables.sql

2. ثم قم بإلغاء التعليق عن الدوال في نهاية هذا الملف وتشغيلها:
   SELECT add_sample_exercise_titles();
   SELECT add_sample_homework_titles();

3. تحقق من النتائج:
   SELECT * FROM get_custom_titles_stats();

📝 يمكنك تخصيص العناوين حسب المحتوى الفعلي لموقعك
🎯 هذه مجرد أمثلة لتوضيح كيفية عمل النظام الجديد
*/
