/**
 * Utilities for handling IDs with spaces in URLs and database queries
 * دوال مساعدة للتعامل مع المعرفات التي تحتوي على مسافات في الروابط واستعلامات قاعدة البيانات
 */

/**
 * Encode ID for use in URLs (handles spaces and special characters)
 * ترميز المعرف للاستخدام في الروابط (يتعامل مع المسافات والرموز الخاصة)
 * @param id - The ID to encode
 * @returns URL-safe encoded ID
 */
export function encodeIdForUrl(id: string | null | undefined): string {
  if (!id || id.trim() === '') {
    return '';
  }

  try {
    // Use encodeURIComponent to handle spaces and special characters
    return encodeURIComponent(id.trim());
  } catch (error) {
    console.warn('Failed to encode ID for URL:', error);
    // Fallback: replace spaces with dashes and remove special characters
    return id.trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '');
  }
}

/**
 * Decode ID from URL (handles URL-encoded spaces and special characters)
 * فك ترميز المعرف من الرابط (يتعامل مع المسافات والرموز المُرمزة)
 * @param encodedId - The URL-encoded ID
 * @returns Decoded ID
 */
export function decodeIdFromUrl(encodedId: string | null | undefined): string {
  if (!encodedId || encodedId.trim() === '') {
    return '';
  }

  try {
    // Use decodeURIComponent to handle URL-encoded characters
    return decodeURIComponent(encodedId.trim());
  } catch (error) {
    console.warn('Failed to decode ID from URL:', error);
    // Fallback: replace dashes with spaces
    return encodedId.trim().replace(/-/g, ' ');
  }
}

/**
 * Sanitize ID for database queries (ensures proper format)
 * تنظيف المعرف لاستعلامات قاعدة البيانات (يضمن التنسيق الصحيح)
 * @param id - The ID to sanitize
 * @returns Sanitized ID
 */
export function sanitizeIdForDatabase(id: string | null | undefined): string {
  if (!id || id.trim() === '') {
    return '';
  }

  // Trim whitespace and normalize
  return id.trim();
}

/**
 * Check if ID is valid (not empty or null)
 * التحقق من صحة المعرف (ليس فارغاً أو null)
 * @param id - The ID to validate
 * @returns True if ID is valid
 */
export function isValidId(id: string | null | undefined): boolean {
  return !!(id && id.trim() !== '');
}

/**
 * Build URL with properly encoded ID
 * بناء رابط مع معرف مُرمز بشكل صحيح
 * @param basePath - The base path (e.g., '/lesson/')
 * @param id - The ID to encode
 * @param queryParams - Optional query parameters
 * @returns Complete URL with encoded ID
 */
export function buildUrlWithId(
  basePath: string, 
  id: string, 
  queryParams?: Record<string, string>
): string {
  if (!isValidId(id)) {
    return basePath;
  }

  const encodedId = encodeIdForUrl(id);
  let url = `${basePath}${encodedId}`;

  if (queryParams && Object.keys(queryParams).length > 0) {
    const searchParams = new URLSearchParams();
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value) {
        searchParams.append(key, value);
      }
    });
    
    const queryString = searchParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }

  return url;
}

/**
 * Extract and decode ID from URL pathname
 * استخراج وفك ترميز المعرف من مسار الرابط
 * @param pathname - The URL pathname
 * @param segmentIndex - The index of the ID segment (default: last segment)
 * @returns Decoded ID or null if not found
 */
export function extractIdFromPathname(pathname: string, segmentIndex?: number): string | null {
  if (!pathname || pathname.trim() === '') {
    return null;
  }

  const segments = pathname.split('/').filter(segment => segment.length > 0);
  
  if (segments.length === 0) {
    return null;
  }

  const targetIndex = segmentIndex !== undefined ? segmentIndex : segments.length - 1;
  const encodedId = segments[targetIndex];

  if (!encodedId) {
    return null;
  }

  return decodeIdFromUrl(encodedId);
}

/**
 * Create safe filename from ID (for downloads, etc.)
 * إنشاء اسم ملف آمن من المعرف (للتحميلات، إلخ)
 * @param id - The ID to convert
 * @param extension - Optional file extension
 * @returns Safe filename
 */
export function createSafeFilename(id: string, extension?: string): string {
  if (!isValidId(id)) {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `file_${timestamp}${extension || ''}`;
  }

  // Replace spaces and special characters with underscores
  const safeId = id.trim()
    .replace(/\s+/g, '_')
    .replace(/[^\w\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF_-]/g, '')
    .substring(0, 100); // Limit length

  return `${safeId}${extension || ''}`;
}

/**
 * Compare two IDs (handles encoding differences)
 * مقارنة معرفين (يتعامل مع اختلافات الترميز)
 * @param id1 - First ID
 * @param id2 - Second ID
 * @returns True if IDs are equivalent
 */
export function compareIds(id1: string | null | undefined, id2: string | null | undefined): boolean {
  const normalizedId1 = sanitizeIdForDatabase(id1);
  const normalizedId2 = sanitizeIdForDatabase(id2);
  
  return normalizedId1 === normalizedId2;
}

/**
 * Batch encode multiple IDs for URLs
 * ترميز متعدد للمعرفات للروابط
 * @param ids - Array of IDs to encode
 * @returns Array of encoded IDs
 */
export function batchEncodeIds(ids: string[]): string[] {
  return ids.map(id => encodeIdForUrl(id)).filter(id => id !== '');
}

/**
 * Batch decode multiple IDs from URLs
 * فك ترميز متعدد للمعرفات من الروابط
 * @param encodedIds - Array of encoded IDs to decode
 * @returns Array of decoded IDs
 */
export function batchDecodeIds(encodedIds: string[]): string[] {
  return encodedIds.map(id => decodeIdFromUrl(id)).filter(id => id !== '');
}
