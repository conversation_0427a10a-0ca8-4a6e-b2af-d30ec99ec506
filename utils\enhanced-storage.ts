// تحسين نظام التخزين المؤقت مع ضغط البيانات وإدارة TTL

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number // Time To Live in milliseconds
  compressed?: boolean
}

interface CacheOptions {
  ttl?: number // Default TTL in milliseconds
  compress?: boolean // Whether to compress data
  maxSize?: number // Maximum cache size in items
}

class EnhancedStorage {
  private prefix: string
  private defaultTTL: number
  private maxSize: number
  private compressionEnabled: boolean

  constructor(prefix = 'edu_cache_', options: CacheOptions = {}) {
    this.prefix = prefix
    this.defaultTTL = options.ttl || 10 * 60 * 1000 // 10 minutes default
    this.maxSize = options.maxSize || 100 // 100 items max
    this.compressionEnabled = options.compress || true
  }

  // ضغط البيانات باستخدام JSON + Base64
  private compress(data: any): string {
    try {
      const jsonString = JSON.stringify(data)
      // استخدام btoa للضغط البسيط (يمكن تحسينه لاحقاً)
      return btoa(unescape(encodeURIComponent(jsonString)))
    } catch (error) {
      console.warn('فشل ضغط البيانات:', error)
      return JSON.stringify(data)
    }
  }

  // إلغاء ضغط البيانات
  private decompress(compressedData: string): any {
    try {
      const jsonString = decodeURIComponent(escape(atob(compressedData)))
      return JSON.parse(jsonString)
    } catch (error) {
      console.warn('فشل إلغاء ضغط البيانات:', error)
      // محاولة قراءة البيانات كـ JSON عادي
      try {
        return JSON.parse(compressedData)
      } catch {
        return null
      }
    }
  }

  // حفظ البيانات في التخزين المحلي
  set<T>(key: string, data: T, ttl?: number): boolean {
    try {
      const cacheItem: CacheItem<T | string> = {
        data: this.compressionEnabled ? this.compress(data) : data,
        timestamp: Date.now(),
        ttl: ttl || this.defaultTTL,
        compressed: this.compressionEnabled
      }

      localStorage.setItem(this.prefix + key, JSON.stringify(cacheItem))
      
      // تنظيف التخزين إذا تجاوز الحد الأقصى
      this.cleanup()
      
      return true
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error)
      return false
    }
  }

  // استرجاع البيانات من التخزين المحلي
  get<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return null

      const cacheItem: CacheItem<T | string> = JSON.parse(item)
      
      // التحقق من انتهاء صلاحية البيانات
      if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
        this.remove(key)
        return null
      }

      // إلغاء ضغط البيانات إذا كانت مضغوطة
      if (cacheItem.compressed && typeof cacheItem.data === 'string') {
        return this.decompress(cacheItem.data)
      }

      return cacheItem.data as T
    } catch (error) {
      console.error('خطأ في استرجاع البيانات:', error)
      this.remove(key) // حذف البيانات التالفة
      return null
    }
  }

  // حذف عنصر واحد
  remove(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }

  // التحقق من وجود البيانات وصلاحيتها
  has(key: string): boolean {
    return this.get(key) !== null
  }

  // تنظيف البيانات المنتهية الصلاحية
  cleanup(): void {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
      
      // حذف البيانات المنتهية الصلاحية
      keys.forEach(key => {
        try {
          const item = localStorage.getItem(key)
          if (item) {
            const cacheItem: CacheItem<any> = JSON.parse(item)
            if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
              localStorage.removeItem(key)
            }
          }
        } catch {
          // حذف البيانات التالفة
          localStorage.removeItem(key)
        }
      })

      // إذا تجاوز عدد العناصر الحد الأقصى، احذف الأقدم
      const remainingKeys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
      if (remainingKeys.length > this.maxSize) {
        const itemsWithTimestamp = remainingKeys.map(key => {
          try {
            const item = localStorage.getItem(key)
            const cacheItem: CacheItem<any> = item ? JSON.parse(item) : null
            return {
              key,
              timestamp: cacheItem?.timestamp || 0
            }
          } catch {
            return { key, timestamp: 0 }
          }
        }).sort((a, b) => a.timestamp - b.timestamp)

        // حذف العناصر الأقدم
        const itemsToDelete = itemsWithTimestamp.slice(0, remainingKeys.length - this.maxSize)
        itemsToDelete.forEach(item => localStorage.removeItem(item.key))
      }
    } catch (error) {
      console.error('خطأ في تنظيف التخزين:', error)
    }
  }

  // مسح جميع البيانات المخزنة
  clear(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
    keys.forEach(key => localStorage.removeItem(key))
  }

  // الحصول على معلومات التخزين
  getStats(): {
    totalItems: number
    totalSize: number
    oldestItem: number
    newestItem: number
  } {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
    let totalSize = 0
    let oldestTimestamp = Date.now()
    let newestTimestamp = 0

    keys.forEach(key => {
      try {
        const item = localStorage.getItem(key)
        if (item) {
          totalSize += item.length
          const cacheItem: CacheItem<any> = JSON.parse(item)
          oldestTimestamp = Math.min(oldestTimestamp, cacheItem.timestamp)
          newestTimestamp = Math.max(newestTimestamp, cacheItem.timestamp)
        }
      } catch {
        // تجاهل البيانات التالفة
      }
    })

    return {
      totalItems: keys.length,
      totalSize,
      oldestItem: oldestTimestamp,
      newestItem: newestTimestamp
    }
  }
}

// إنشاء مثيل مُحسن للتخزين
export const enhancedStorage = new EnhancedStorage('edu_cache_', {
  ttl: 10 * 60 * 1000, // 10 دقائق
  compress: true,
  maxSize: 50
})

// دوال مساعدة للاستخدام السهل
export const cacheUtils = {
  // حفظ البيانات مع TTL مخصص
  setWithTTL: <T>(key: string, data: T, minutes: number) => {
    return enhancedStorage.set(key, data, minutes * 60 * 1000)
  },

  // حفظ البيانات لمدة طويلة (يوم واحد)
  setLongTerm: <T>(key: string, data: T) => {
    return enhancedStorage.set(key, data, 24 * 60 * 60 * 1000)
  },

  // حفظ البيانات لمدة قصيرة (دقيقة واحدة)
  setShortTerm: <T>(key: string, data: T) => {
    return enhancedStorage.set(key, data, 60 * 1000)
  },

  // استرجاع البيانات
  get: <T>(key: string): T | null => {
    return enhancedStorage.get<T>(key)
  },

  // حذف البيانات
  remove: (key: string) => {
    enhancedStorage.remove(key)
  },

  // تنظيف التخزين
  cleanup: () => {
    enhancedStorage.cleanup()
  },

  // مسح جميع البيانات
  clear: () => {
    enhancedStorage.clear()
  },

  // الحصول على إحصائيات
  getStats: () => {
    return enhancedStorage.getStats()
  }
}

// تشغيل تنظيف دوري كل 5 دقائق
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheUtils.cleanup()
  }, 5 * 60 * 1000)
}
