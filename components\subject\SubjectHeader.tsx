'use client';

import type { Subject, Year } from '@/data/types';

interface SubjectHeaderProps {
  subject: Subject;
  year: Year | null;
}

const SubjectHeader = ({ subject, year }: SubjectHeaderProps) => {
  return (
    <div className="text-center mb-8">
      <div className="flex justify-center items-center gap-3">
        <span className="text-5xl">{subject.icon}</span>
        <h1 className="text-4xl font-bold text-primary">{subject.name}</h1>
      </div>
      <p className="text-muted-foreground mt-2">محتوى مادة {subject.name} للصف {year?.name || ''}</p>
    </div>
  );
};

export default SubjectHeader;
