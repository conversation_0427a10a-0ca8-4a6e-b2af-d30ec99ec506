import { NextRequest, NextResponse } from 'next/server';
import { getSingleLessonServerSide } from '@/backend/utils/dataLoader';
import { decodeIdFromUrl } from '@/utils/id-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { lessonId: string } }
) {
  try {
    const { lessonId: encodedLessonId } = params;

    if (!encodedLessonId) {
      return NextResponse.json(
        { error: 'lessonId is required' },
        { status: 400 }
      );
    }

    // فك ترميز المعرف من الرابط
    const lessonId = decodeIdFromUrl(encodedLessonId);

    console.log(`API: جاري جلب الدرس ${lessonId}...`);

    // جلب الدرس مباشرة من Supabase
    const lesson = await getSingleLessonServerSide(lessonId);
    
    if (!lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      );
    }

    console.log(`API: تم جلب الدرس "${lesson.title}" بنجاح`);
    
    return NextResponse.json({
      success: true,
      lesson: {
        id: lesson.id,
        title: lesson.title,
        description: lesson.description,
        subjectId: lesson.subjectId,
        content_type: lesson.content_type,
        display_order: lesson.display_order,
        exercises: lesson.exercises
      }
    });
  } catch (error) {
    console.error('Error fetching lesson:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
