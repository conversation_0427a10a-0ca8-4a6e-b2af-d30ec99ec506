'use client'

import React from 'react';
import ContentCard from '@/components/ui/content-card';
import type { Exercise } from '@/data/types';

interface HomeworkCardProps {
  exercise: Exercise;
  index: number;
  lessonTitle?: string;
}

export default function HomeworkCard({ exercise, index, lessonTitle }: HomeworkCardProps) {
  return (
    <ContentCard
      exerciseImageUrl={exercise.exerciseImageUrl}
      solutionImageUrl={exercise.solutionImageUrl}
      index={index}
      contentType="homework"
      lessonTitle={lessonTitle}
      customTitle={exercise.title}
    />
  );
}
