import { Level, Year, Subject, Lesson } from '@/data/types';

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  LEVELS: 'arab-edu-levels',
  YEARS: 'arab-edu-years',
  SUBJECTS: 'arab-edu-subjects',
  LESSONS: 'arab-edu-lessons',
  LESSONS_BY_SUBJECT: 'arab-edu-lessons-by-subject-',
  YEARS_BY_LEVEL: 'arab-edu-years-by-level-',
  SUBJECTS_BY_YEAR: 'arab-edu-subjects-by-year-',
  LAST_FETCH_TIME_PREFIX: 'arab-edu-last-fetch-time-',
};

// مدة صلاحية البيانات المخزنة (بالمللي ثانية)
// 1 ساعة افتراضياً
const CACHE_EXPIRY_TIME = 1 * 60 * 60 * 1000;

// التحقق من وجود localStorage
const isLocalStorageAvailable = (): boolean => {
  try {
    const testKey = '__test__';
    localStorage.setItem(testKey, testKey);
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    console.warn('localStorage غير متاح في هذا المتصفح');
    return false;
  }
};

// حفظ البيانات في التخزين المحلي
export const saveToLocalStorage = <T>(key: string, data: T): void => {
  if (!isLocalStorageAvailable()) return;

  try {
    const serializedData = JSON.stringify(data);
    localStorage.setItem(key, serializedData);
    console.log(`تم حفظ البيانات في التخزين المحلي: ${key}`);
  } catch (e) {
    console.error(`خطأ في حفظ البيانات في التخزين المحلي: ${key}`, e);
  }
};

// استرجاع البيانات من التخزين المحلي
export const getFromLocalStorage = <T>(key: string): T | null => {
  if (!isLocalStorageAvailable()) return null;

  try {
    const serializedData = localStorage.getItem(key);
    if (!serializedData) return null;

    return JSON.parse(serializedData) as T;
  } catch (e) {
    console.error(`خطأ في استرجاع البيانات من التخزين المحلي: ${key}`, e);
    return null;
  }
};

// حفظ وقت آخر جلب للبيانات
export const saveLastFetchTime = (key: string): void => {
  if (!isLocalStorageAvailable()) return;

  try {
    const currentTime = new Date().getTime();
    localStorage.setItem(STORAGE_KEYS.LAST_FETCH_TIME_PREFIX + key, currentTime.toString());
    console.log(`تم تحديث وقت آخر جلب للبيانات (${key}): ${new Date(currentTime).toLocaleString()}`);
  } catch (e) {
    console.error(`خطأ في حفظ وقت آخر جلب للبيانات (${key})`, e);
  }
};

// التحقق من صلاحية البيانات المخزنة
export const isCacheValid = (key: string): boolean => {
  if (!isLocalStorageAvailable()) return false;

  try {
    const lastFetchTimeStr = localStorage.getItem(STORAGE_KEYS.LAST_FETCH_TIME_PREFIX + key);
    if (!lastFetchTimeStr) return false;

    const lastFetchTime = parseInt(lastFetchTimeStr, 10);
    const currentTime = new Date().getTime();
    const cacheAge = currentTime - lastFetchTime;

    return cacheAge < CACHE_EXPIRY_TIME;
  } catch (e) {
    console.error(`خطأ في التحقق من صلاحية البيانات المخزنة (${key})`, e);
    return false;
  }
};

// حفظ المستويات في التخزين المحلي
export const saveLevels = (levels: Level[]): void => {
  saveToLocalStorage(STORAGE_KEYS.LEVELS, levels);
  saveLastFetchTime('levels');
};

// حفظ السنوات في التخزين المحلي
export const saveYears = (years: Year[]): void => {
  saveToLocalStorage(STORAGE_KEYS.YEARS, years);
  saveLastFetchTime('years');
};

// حفظ المواد في التخزين المحلي
export const saveSubjects = (subjects: Subject[]): void => {
  saveToLocalStorage(STORAGE_KEYS.SUBJECTS, subjects);
  saveLastFetchTime('subjects');
};

// حفظ الدروس في التخزين المحلي
export const saveLessons = (lessons: Lesson[]): void => {
  saveToLocalStorage(STORAGE_KEYS.LESSONS, lessons);
  saveLastFetchTime('lessons');
};

// حفظ السنوات حسب المستوى
export const saveYearsByLevel = (levelId: string, years: Year[]): void => {
  saveToLocalStorage(STORAGE_KEYS.YEARS_BY_LEVEL + levelId, years);
  saveLastFetchTime('years-by-level-' + levelId);
};

// حفظ المواد حسب السنة
export const saveSubjectsByYear = (yearId: string, subjects: Subject[]): void => {
  saveToLocalStorage(STORAGE_KEYS.SUBJECTS_BY_YEAR + yearId, subjects);
  saveLastFetchTime('subjects-by-year-' + yearId);
};

// حفظ الدروس حسب المادة
export const saveLessonsBySubject = (subjectId: string, lessons: Lesson[]): void => {
  saveToLocalStorage(STORAGE_KEYS.LESSONS_BY_SUBJECT + subjectId, lessons);
  saveLastFetchTime('lessons-by-subject-' + subjectId);
};

// استرجاع المستويات من التخزين المحلي
export const getLevelsFromStorage = (): Level[] | null => {
  if (!isCacheValid('levels')) return null;
  return getFromLocalStorage<Level[]>(STORAGE_KEYS.LEVELS);
};

// استرجاع السنوات من التخزين المحلي
export const getYearsFromStorage = (): Year[] | null => {
  if (!isCacheValid('years')) return null;
  return getFromLocalStorage<Year[]>(STORAGE_KEYS.YEARS);
};

// استرجاع المواد من التخزين المحلي
export const getSubjectsFromStorage = (): Subject[] | null => {
  if (!isCacheValid('subjects')) return null;
  return getFromLocalStorage<Subject[]>(STORAGE_KEYS.SUBJECTS);
};

// استرجاع الدروس من التخزين المحلي
export const getLessonsFromStorage = (): Lesson[] | null => {
  if (!isCacheValid('lessons')) return null;
  return getFromLocalStorage<Lesson[]>(STORAGE_KEYS.LESSONS);
};

// استرجاع السنوات حسب المستوى من التخزين المحلي
export const getYearsByLevelFromStorage = (levelId: string): Year[] | null => {
  if (!isCacheValid('years-by-level-' + levelId)) return null;
  return getFromLocalStorage<Year[]>(STORAGE_KEYS.YEARS_BY_LEVEL + levelId);
};

// استرجاع المواد حسب السنة من التخزين المحلي
export const getSubjectsByYearFromStorage = (yearId: string): Subject[] | null => {
  if (!isCacheValid('subjects-by-year-' + yearId)) return null;
  return getFromLocalStorage<Subject[]>(STORAGE_KEYS.SUBJECTS_BY_YEAR + yearId);
};

// استرجاع الدروس حسب المادة من التخزين المحلي
export const getLessonsBySubjectFromStorage = (subjectId: string): Lesson[] | null => {
  if (!isCacheValid('lessons-by-subject-' + subjectId)) return null;
  return getFromLocalStorage<Lesson[]>(STORAGE_KEYS.LESSONS_BY_SUBJECT + subjectId);
};

// مسح جميع البيانات من التخزين المحلي
export const clearAllData = (): void => {
  if (!isLocalStorageAvailable()) return;

  try {
    // مسح البيانات الرئيسية
    localStorage.removeItem(STORAGE_KEYS.LEVELS);
    localStorage.removeItem(STORAGE_KEYS.YEARS);
    localStorage.removeItem(STORAGE_KEYS.SUBJECTS);
    localStorage.removeItem(STORAGE_KEYS.LESSONS);

    // مسح البيانات المخزنة بمفاتيح ديناميكية
    for (const key of Object.keys(localStorage)) {
      if (key.startsWith(STORAGE_KEYS.YEARS_BY_LEVEL) ||
          key.startsWith(STORAGE_KEYS.SUBJECTS_BY_YEAR) ||
          key.startsWith(STORAGE_KEYS.LESSONS_BY_SUBJECT) ||
          key.startsWith(STORAGE_KEYS.LAST_FETCH_TIME_PREFIX)) {
        localStorage.removeItem(key);
      }
    }

    console.log('تم مسح جميع البيانات من التخزين المحلي');
  } catch (e) {
    console.error('خطأ في مسح البيانات من التخزين المحلي', e);
  }
};

// تصدير المفاتيح للاستخدام الخارجي
export { STORAGE_KEYS };
