-- سكريبت نقل بيانات الامتحانات إلى ملخص الدروس
-- Migration Script: Convert Exams to Lesson Summaries

-- 1. إنشاء جدول ملخص الدروس (summaries)
CREATE TABLE IF NOT EXISTS public.summaries (
    id text PRIMARY KEY,
    lesson_id text REFERENCES public.lessons(id) ON DELETE CASCADE,
    hint text,
    exercise_image_url text,
    solution_image_url text,
    options text[],
    question text,
    solution text,
    student_input_type text NOT NULL DEFAULT 'text',
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 2. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS summaries_lesson_id_idx ON public.summaries(lesson_id);
CREATE INDEX IF NOT EXISTS summaries_created_at_idx ON public.summaries(created_at);

-- 3. تفعيل Row Level Security (RLS)
ALTER TABLE public.summaries ENABLE ROW LEVEL SECURITY;

-- 4. إنشاء سياسات الأمان (Security Policies)
-- سياسة القراءة للجميع
CREATE POLICY "Enable read access for all users" ON public.summaries
    FOR SELECT USING (true);

-- سياسة الكتابة للمستخدمين المصرح لهم (يمكن تعديلها حسب الحاجة)
CREATE POLICY "Enable insert for authenticated users only" ON public.summaries
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON public.summaries
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON public.summaries
    FOR DELETE USING (auth.role() = 'authenticated');

-- 5. نقل البيانات من جدول exams إلى summaries
INSERT INTO public.summaries (
    id,
    lesson_id,
    hint,
    exercise_image_url,
    solution_image_url,
    options,
    question,
    solution,
    student_input_type
)
SELECT 
    id,
    lesson_id,
    hint,
    exercise_image_url,
    solution_image_url,
    options,
    question,
    solution,
    student_input_type
FROM public.exams
WHERE NOT EXISTS (
    SELECT 1 FROM public.summaries WHERE summaries.id = exams.id
);

-- 6. تحديث نوع المحتوى في جدول lessons
UPDATE public.lessons 
SET content_type = 'summary' 
WHERE content_type = 'exam';

-- 7. إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. إنشاء trigger لتحديث updated_at
CREATE TRIGGER handle_summaries_updated_at
    BEFORE UPDATE ON public.summaries
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- 9. إضافة تعليقات للتوثيق
COMMENT ON TABLE public.summaries IS 'جدول ملخص الدروس - يحتوي على عناصر ملخص الدروس التعليمية';
COMMENT ON COLUMN public.summaries.id IS 'المعرف الفريد لعنصر الملخص';
COMMENT ON COLUMN public.summaries.lesson_id IS 'معرف الدرس المرتبط بالملخص';
COMMENT ON COLUMN public.summaries.hint IS 'تلميح أو ملاحظة للملخص';
COMMENT ON COLUMN public.summaries.exercise_image_url IS 'رابط صورة أو ملف PDF للملخص';
COMMENT ON COLUMN public.summaries.solution_image_url IS 'رابط صورة أو ملف PDF للحل أو التوضيح';

-- 10. عرض النتائج
SELECT 
    'تم إنشاء جدول summaries بنجاح' as status,
    COUNT(*) as total_summaries
FROM public.summaries;

-- 11. التحقق من البيانات المنقولة
SELECT 
    l.title as lesson_title,
    l.content_type,
    COUNT(s.id) as summaries_count
FROM public.lessons l
LEFT JOIN public.summaries s ON l.id = s.lesson_id
WHERE l.content_type = 'summary'
GROUP BY l.id, l.title, l.content_type
ORDER BY l.title;

-- ملاحظة: بعد التأكد من نجاح النقل، يمكن حذف جدول exams
-- DROP TABLE IF EXISTS public.exams;
