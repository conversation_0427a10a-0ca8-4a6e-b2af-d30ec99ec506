import { Suspense } from 'react'
import { Metadata } from 'next'
import SubjectClient from './subject-client'
import { getSubjectServerSide, getYearServerSide } from '@/backend/api/educationAPI'
import { generateSubjectMetadata } from '@/lib/seo'
import { decodeIdFromUrl } from '@/utils/id-utils'

type Props = {
  params: { subjectId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const subjectId = decodeIdFromUrl(params.subjectId);
    const subject = await getSubjectServerSide(subjectId)
    if (!subject) {
      return {
        title: 'المادة الدراسية غير موجودة - منصة التعليم العربي',
        description: 'لم يتم العثور على المادة الدراسية المطلوبة',
        robots: { index: false, follow: false }
      }
    }

    const year = await getYearServerSide(subject.yearId)
    return generateSubjectMetadata(subject, year)
  } catch (error) {
    console.error('Error generating subject metadata:', error)
    return {
      title: 'المادة الدراسية - منصة التعليم العربي',
      description: 'استكشف دروس وتمارين المادة الدراسية',
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function SubjectPage({ params }: Props) {
  const subjectId = decodeIdFromUrl(params.subjectId);

  return (
    <Suspense fallback={<PageLoader />}>
      <SubjectClient subjectId={subjectId} />
    </Suspense>
  )
}
