'use client'

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { ArrowLeft, GraduationCap } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ExamList from '@/components/exam/ExamList';
import { useLesson, useSubject, useYear, useLevel } from '@/hooks/use-education-data';
import { isExamLessonAccessibleDirect } from '@/utils/examFilter';
import { useViewerMode } from '@/hooks/use-viewer-mode';

interface ExamClientProps {
  lessonId: string;
}

export default function ExamClient({ lessonId }: ExamClientProps) {
  const { isViewerMode } = useViewerMode();
  const searchParams = useSearchParams();

  // الحصول على رابط العودة من معاملات URL
  const returnTo = searchParams.get('returnTo');

  // استخدام React Query hooks للحصول على البيانات
  const { data: lesson, isLoading: lessonLoading } = useLesson(lessonId);
  const { data: subject, isLoading: subjectLoading } = useSubject(lesson?.subjectId || '');
  const { data: year, isLoading: yearLoading } = useYear(subject?.yearId || '');
  const { data: level, isLoading: levelLoading } = useLevel(year?.levelId || '');

  const loading = lessonLoading || subjectLoading || yearLoading || levelLoading;

  // فحص إمكانية الوصول للامتحان
  const isAccessible = lesson ? isExamLessonAccessibleDirect(lesson) : true;



  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg">جاري تحميل البيانات...</p>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  if (!lesson || !isAccessible) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <GraduationCap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">
            {!isAccessible ? 'الامتحان غير متاح' : 'الامتحان غير موجود'}
          </h1>
          <p className="text-muted-foreground mb-6">
            {!isAccessible ?
              'الامتحانات متاحة فقط للسادس ابتدائي، الثالث إعدادي، الأولى باك، والثانية باك' :
              'عذراً، لم يتم العثور على الامتحان المطلوب'
            }
          </p>
          <Link
            href="/levels"
            className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى المراحل الدراسية
          </Link>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  console.log(`عدد أسئلة الامتحان المتاحة في "${lesson.title}": ${lesson.exercises?.length || 0}`);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {!isViewerMode && <Header />}

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">{lesson.title}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            {lesson.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground">
            <Link href="/levels" className="hover:text-primary transition-colors">
              المستويات
            </Link>
            <span>/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors">
                  {year.name}
                </Link>
                <span>/</span>
              </>
            )}
            {subject && (
              <>
                <Link href={returnTo || `/subject/${subject.id}`} className="hover:text-primary transition-colors">
                  {subject.name}
                </Link>
                <span>/</span>
              </>
            )}
            <span className="text-foreground">{lesson.title}</span>
          </nav>
        </div>

        {/* Exam Questions */}
        {lesson.exercises.length > 0 ? (
          <ExamList
            exercises={lesson.exercises}
            lessonTitle={lesson.title}
            subjectName={subject?.name}
            yearName={year?.name}
            levelName={level?.name}
          />
        ) : (
          <div className="text-center py-12">
            <GraduationCap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">لا توجد أسئلة امتحان</h3>
            <p className="text-muted-foreground mb-6">
              لم يتم إضافة أسئلة امتحان لهذا الدرس بعد
            </p>
            {subject && (
              <Link
                href={returnTo || `/subject/${subject.id}`}
                className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة إلى المادة
              </Link>
            )}
          </div>
        )}
      </div>

      {!isViewerMode && (
        <div className="mt-auto">
          <Footer />
        </div>
      )}
    </div>
  );
}
