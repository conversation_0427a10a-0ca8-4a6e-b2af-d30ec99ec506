import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// قائمة بالصفحات التي يجب فحصها للمحتوى الفارغ
const CONTENT_PAGES = ['/lesson/', '/homework/', '/summary/', '/exam/']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // فحص إذا كان المسار يحتوي على صفحة محتوى
  const isContentPage = CONTENT_PAGES.some(page => pathname.startsWith(page))
  
  if (isContentPage) {
    // إضافة header لمنع الأرشفة للصفحات الفارغة
    const response = NextResponse.next()
    
    // إضافة meta tag لمنع الأرشفة إذا كانت الصفحة فارغة
    response.headers.set('X-Content-Check', 'true')
    
    return response
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/lesson/:path*',
    '/homework/:path*', 
    '/summary/:path*',
    '/exam/:path*'
  ]
}
