'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Image, Maximize2, FileText, X } from 'lucide-react';
import { ImageViewer } from '@/components/ui/image-viewer';
import { usePdfHandler } from '@/hooks/use-pdf-handler';

interface ExerciseProps {
  id: string;
  title?: string;
  question?: string;
  solution?: string;
  exerciseImageUrl?: string;
  solutionImageUrl?: string;
  index: number;
}

const ExerciseCard = ({ id, title, question, solution, exerciseImageUrl, solutionImageUrl, index }: ExerciseProps) => {
  const [showSolution, setShowSolution] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | undefined>(undefined);
  const [currentAlt, setCurrentAlt] = useState('');

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { isPdf, handleDownload: _handleDownload } = usePdfHandler();

  // Check if solution is available - handle empty strings and null/undefined
  const hasSolution = Boolean(
    (solution && solution.trim() !== '') ||
    (solutionImageUrl && solutionImageUrl.trim() !== '')
  );

  // Debug logging to help identify the issue (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log(`Exercise ${index + 1}:`, {
      id,
      solution: solution || 'undefined',
      solutionImageUrl: solutionImageUrl || 'undefined',
      hasSolution
    });
  }

  // Reset showSolution when hasSolution changes
  useEffect(() => {
    if (!hasSolution && showSolution) {
      setShowSolution(false);
    }
  }, [hasSolution, showSolution]);

  const toggleSolution = () => {
    if (hasSolution) {
      setShowSolution(!showSolution);
    }
  };

  const openImageViewer = (imageUrl: string | undefined, alt: string) => {
    if (imageUrl) {
      setCurrentImage(imageUrl);
      setCurrentAlt(alt);
      setViewerOpen(true);
    }
  };

  const renderMediaContent = (url: string, altText: string, isForSolution = false) => {
    if (isPdf(url)) {
      return (
        <div
          className="rounded-md p-4 bg-muted/30 flex flex-col items-center justify-center mx-auto relative"
          style={{ minHeight: '120px' }}
        >
          <FileText className="h-12 w-12 text-primary mb-2" />
          <p className="text-center text-muted-foreground mb-3">
            ملف PDF {isForSolution ? 'للحل' : 'للتمرين'}
          </p>

          <div className="flex flex-wrap gap-2 mt-2 justify-center">
            <button
              onClick={() => openImageViewer(url, altText)}
              className="bg-primary text-primary-foreground hover:bg-primary/90 px-3 py-1.5 rounded-md text-sm flex items-center gap-1"
              title="عرض ملف PDF"
            >
              <Maximize2 className="h-4 w-4" />
              <span>عرض</span>
            </button>
          </div>
        </div>
      );
    }

    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={url}
        alt={altText}
        className="rounded-md max-w-full h-auto cursor-pointer mx-auto block"
        style={{ margin: '0 auto' }}
        onClick={() => openImageViewer(url, altText)}
        onError={(e) => {
          console.error("فشل تحميل الصورة:", url);
          (e.target as HTMLImageElement).style.display = "none";
        }}
      />
    );
  };

  return (
    <>
      <Card className="rtl-card" dir="rtl">
        <CardContent className="p-6">
          <div className="mb-4">
            <h3 className="text-xl font-bold mb-3 text-primary text-right arabic-heading">{title || `تمرين ${index + 1}`}:</h3>
          </div>

          {/* Exercise Content */}
          {exerciseImageUrl ? (
            <div className="mb-6 relative group">
              {renderMediaContent(exerciseImageUrl, `صورة توضيحية للتمرين ${index + 1}`)}
              <button
                className="absolute top-2 left-2 bg-background/80 p-1.5 rounded-md opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => openImageViewer(exerciseImageUrl, isPdf(exerciseImageUrl) ? `ملف PDF للتمرين ${index + 1}` : `صورة توضيحية للتمرين ${index + 1}`)}
                title={isPdf(exerciseImageUrl) ? "عرض ملف PDF" : "عرض الصورة بحجم أكبر"}
              >
                {isPdf(exerciseImageUrl) ? (
                  <FileText className="h-4 w-4 text-primary" />
                ) : (
                  <Maximize2 className="h-4 w-4 text-primary" />
                )}
              </button>
            </div>
          ) : (
            <div className="mb-6 flex items-center justify-center bg-muted rounded-md p-6">
              {/* eslint-disable-next-line jsx-a11y/alt-text */}
              <Image className="h-12 w-12 text-muted-foreground opacity-50" />
            </div>
          )}

          {question && (
            <div className="mb-4">
              <p className="text-foreground">{question}</p>
            </div>
          )}

          <div className="flex flex-wrap gap-3 justify-start">
            <Button
              onClick={toggleSolution}
              variant="outline"
              disabled={!hasSolution}
              className={`rtl-button flex items-center gap-2 transition-all duration-200 ${
                hasSolution
                  ? "border-primary text-primary hover:bg-primary/20 hover:text-primary focus:text-primary active:text-primary focus-visible:text-primary focus:ring-0 focus:ring-offset-0 focus:outline-none focus:bg-transparent"
                  : "border-muted-foreground/30 text-muted-foreground cursor-not-allowed opacity-60 hover:opacity-60"
              }`}
              title={!hasSolution ? "لا يوجد حل متاح لهذا التمرين" : ""}
            >
              {showSolution ? (
                <>
                  إخفاء الحل
                  <EyeOff className="w-4 h-4" />
                </>
              ) : (
                <>
                  {hasSolution ? "عرض الحل" : "لا يوجد حل"}
                  {hasSolution ? <Eye className="w-4 h-4" /> : <X className="w-4 h-4" />}
                </>
              )}
            </Button>
          </div>

          {showSolution && (
            <div className="mt-4 p-4 rounded-md bg-muted rtl-container">
              <h4 className="font-bold mb-3 text-primary text-right arabic-heading">الحل:</h4>

              {/* Solution Content */}
              {solutionImageUrl ? (
                <div className="mt-2 mb-3 relative group">
                  {renderMediaContent(solutionImageUrl, `الحل للتمرين ${index + 1}`, true)}
                  <button
                    className="absolute top-2 left-2 bg-background/80 p-1.5 rounded-md opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => openImageViewer(solutionImageUrl, isPdf(solutionImageUrl) ? `ملف PDF للحل ${index + 1}` : `الحل للتمرين ${index + 1}`)}
                    title={isPdf(solutionImageUrl) ? "عرض ملف PDF" : "عرض الصورة بحجم أكبر"}
                  >
                    {isPdf(solutionImageUrl) ? (
                      <FileText className="h-4 w-4 text-primary" />
                    ) : (
                      <Maximize2 className="h-4 w-4 text-primary" />
                    )}
                  </button>
                </div>
              ) : (
                <div className="mb-3 flex items-center justify-center bg-muted/50 rounded-md p-6">
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image className="h-12 w-12 text-muted-foreground opacity-50" />
                </div>
              )}

              {solution && <p className="text-foreground text-right">{solution}</p>}
              {!solution && !solutionImageUrl && (
                <p className="text-muted-foreground italic text-right">لم يتم توفير حل نصي لهذا التمرين</p>
              )}
              {solutionImageUrl && isPdf(solutionImageUrl) && (
                <p className="text-muted-foreground italic text-right">يرجى الاطلاع على ملف PDF للحل</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Image Viewer */}
      <ImageViewer
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        imageUrl={currentImage || ''}
        alt={currentAlt}
      />
    </>
  );
};

export default ExerciseCard;
