'use client'

import { useEffect, useState, useCallback } from 'react'

interface PerformanceMetrics {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
  loadTime?: number
  domContentLoaded?: number
}

interface ResourceTiming {
  name: string
  duration: number
  size?: number
  type: string
}

export function AdvancedPerformanceOptimizer() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})
  const [resources, setResources] = useState<ResourceTiming[]>([])
  const [isOptimizing, setIsOptimizing] = useState(false)

  const collectPerformanceMetrics = useCallback(() => {
    if (!window.performance) return

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')

    const newMetrics: PerformanceMetrics = {
      ttfb: navigation?.responseStart - navigation?.requestStart,
      loadTime: navigation?.loadEventEnd - navigation?.fetchStart,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.fetchStart,
    }

    // First Contentful Paint
    const fcpEntry = paint.find(entry => entry.name === 'first-contentful-paint')
    if (fcpEntry) {
      newMetrics.fcp = fcpEntry.startTime
    }

    setMetrics(newMetrics)
  }, [])

  const optimizeResources = useCallback(() => {
    if (!window.performance) return

    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    const optimizedResources: ResourceTiming[] = []

    resourceEntries.forEach(entry => {
      const resource: ResourceTiming = {
        name: entry.name.split('/').pop() || entry.name,
        duration: entry.duration,
        size: entry.transferSize,
        type: getResourceType(entry.name)
      }

      // تحديد الموارد البطيئة
      if (entry.duration > 1000) {
        console.warn(`🐌 مورد بطيء: ${resource.name} (${resource.duration.toFixed(2)}ms)`)
      }

      // تحديد الموارد الكبيرة
      if (entry.transferSize && entry.transferSize > 500000) {
        console.warn(`📦 مورد كبير: ${resource.name} (${(entry.transferSize / 1024).toFixed(2)}KB)`)
      }

      optimizedResources.push(resource)
    })

    setResources(optimizedResources)
  }, [])

  const setupPerformanceObservers = useCallback(() => {
    if (!('PerformanceObserver' in window)) return

    try {
      // Largest Contentful Paint Observer
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number }
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }))

          // تحذير إذا كان LCP بطيء
          if (lastEntry.startTime > 2500) {
            console.warn(`🐌 LCP بطيء: ${lastEntry.startTime.toFixed(2)}ms`)
          }
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay Observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: PerformanceEntry & { processingStart?: number }) => {
          if (entry.processingStart) {
            const fid = entry.processingStart - entry.startTime
            setMetrics(prev => ({ ...prev, fid }))

            // تحذير إذا كان FID بطيء
            if (fid > 100) {
              console.warn(`🐌 FID بطيء: ${fid.toFixed(2)}ms`)
            }
          }
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift Observer
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        list.getEntries().forEach((entry: PerformanceEntry & { hadRecentInput?: boolean; value?: number }) => {
          if (!entry.hadRecentInput && entry.value) {
            clsValue += entry.value
          }
        })
        setMetrics(prev => ({ ...prev, cls: clsValue }))

        // تحذير إذا كان CLS عالي
        if (clsValue > 0.1) {
          console.warn(`📐 CLS عالي: ${clsValue.toFixed(3)}`)
        }
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })

    } catch (error) {
      console.warn('Performance Observer غير مدعوم:', error)
    }
  }, [])

  useEffect(() => {
    // تشغيل فقط في بيئة التطوير أو عند الحاجة
    if (process.env.NODE_ENV !== 'development' && !window.location.search.includes('debug=true')) {
      return
    }

    collectPerformanceMetrics()
    optimizeResources()
    setupPerformanceObservers()
  }, [collectPerformanceMetrics, optimizeResources, setupPerformanceObservers])



  const getResourceType = (url: string): string => {
    if (url.includes('.js')) return 'JavaScript'
    if (url.includes('.css')) return 'CSS'
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'Image'
    if (url.includes('.pdf')) return 'PDF'
    if (url.includes('font')) return 'Font'
    return 'Other'
  }

  const optimizeCurrentPage = async () => {
    setIsOptimizing(true)
    
    try {
      // تنظيف الذاكرة
      if ('gc' in window && typeof window.gc === 'function') {
        window.gc()
      }

      // تحسين الصور
      const images = document.querySelectorAll('img')
      images.forEach(img => {
        if (!img.loading) {
          img.loading = 'lazy'
        }
        if (!img.decoding) {
          img.decoding = 'async'
        }
      })

      // تحسين الخطوط
      const links = document.querySelectorAll('link[rel="stylesheet"]')
      links.forEach(link => {
        if (!link.getAttribute('media')) {
          link.setAttribute('media', 'print')
          link.setAttribute('onload', "this.media='all'")
        }
      })

      // تحسين التخزين المؤقت
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        console.log('📦 Cache متاح:', cacheNames.length, 'caches')
      }

      console.log('✅ تم تحسين الصفحة الحالية')
    } catch (error) {
      console.error('❌ خطأ في تحسين الصفحة:', error)
    } finally {
      setIsOptimizing(false)
    }
  }



  // عرض معلومات الأداء في بيئة التطوير فقط
  if (process.env.NODE_ENV !== 'development' && !window.location.search.includes('debug=true')) {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-bold">🚀 مراقب الأداء</h3>
        <button
          onClick={optimizeCurrentPage}
          disabled={isOptimizing}
          className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs disabled:opacity-50"
        >
          {isOptimizing ? '⏳' : '🔧'} تحسين
        </button>
      </div>
      
      <div className="space-y-1">
        {metrics.fcp && (
          <div className={`flex justify-between ${metrics.fcp > 1800 ? 'text-red-400' : metrics.fcp > 1000 ? 'text-yellow-400' : 'text-green-400'}`}>
            <span>FCP:</span>
            <span>{metrics.fcp.toFixed(0)}ms</span>
          </div>
        )}
        
        {metrics.lcp && (
          <div className={`flex justify-between ${metrics.lcp > 2500 ? 'text-red-400' : metrics.lcp > 1500 ? 'text-yellow-400' : 'text-green-400'}`}>
            <span>LCP:</span>
            <span>{metrics.lcp.toFixed(0)}ms</span>
          </div>
        )}
        
        {metrics.fid && (
          <div className={`flex justify-between ${metrics.fid > 100 ? 'text-red-400' : metrics.fid > 50 ? 'text-yellow-400' : 'text-green-400'}`}>
            <span>FID:</span>
            <span>{metrics.fid.toFixed(0)}ms</span>
          </div>
        )}
        
        {metrics.cls !== undefined && (
          <div className={`flex justify-between ${metrics.cls > 0.1 ? 'text-red-400' : metrics.cls > 0.05 ? 'text-yellow-400' : 'text-green-400'}`}>
            <span>CLS:</span>
            <span>{metrics.cls.toFixed(3)}</span>
          </div>
        )}
        
        {metrics.loadTime && (
          <div className="flex justify-between text-blue-400">
            <span>Load:</span>
            <span>{metrics.loadTime.toFixed(0)}ms</span>
          </div>
        )}
      </div>
      
      <div className="mt-2 pt-2 border-t border-gray-600">
        <div className="text-xs text-gray-400">
          الموارد: {resources.length} | 
          بطيئة: {resources.filter(r => r.duration > 1000).length}
        </div>
      </div>
    </div>
  )
}
