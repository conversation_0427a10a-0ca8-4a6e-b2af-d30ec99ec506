/**
 * نظام تحويل الروابط النسبية إلى روابط كاملة
 * File URL Resolver - Convert relative URLs to absolute URLs
 */

// إعدادات خادم الملفات
interface FileServerConfig {
  // الخادم الأساسي للملفات
  baseUrl: string;
  // النطاقات البديلة (للتطوير والإنتاج)
  fallbackDomains: string[];
  // بادئة المجلد (إذا كانت الملفات في مجلد فرعي)
  pathPrefix?: string;
}

// إعدادات افتراضية للخادم
const DEFAULT_FILE_SERVER_CONFIG: FileServerConfig = {
  baseUrl: process.env.NEXT_PUBLIC_FILE_SERVER_URL || 'https://d.talamid.ma',
  fallbackDomains: [
    'https://d.talamid.ma',
    'https://files.talamid.ma',
    'https://cdn.talamid.ma'
  ],
  pathPrefix: process.env.NEXT_PUBLIC_FILE_PATH_PREFIX || ''
};

// إعدادات قابلة للتخصيص من متغيرات البيئة
const FILE_SERVER_CONFIG: FileServerConfig = {
  baseUrl: process.env.NEXT_PUBLIC_FILE_SERVER_URL || DEFAULT_FILE_SERVER_CONFIG.baseUrl,
  fallbackDomains: process.env.NEXT_PUBLIC_FILE_FALLBACK_DOMAINS 
    ? process.env.NEXT_PUBLIC_FILE_FALLBACK_DOMAINS.split(',')
    : DEFAULT_FILE_SERVER_CONFIG.fallbackDomains,
  pathPrefix: process.env.NEXT_PUBLIC_FILE_PATH_PREFIX || DEFAULT_FILE_SERVER_CONFIG.pathPrefix
};

/**
 * تحويل رابط نسبي إلى رابط كامل
 * Convert relative URL to absolute URL
 */
export function resolveFileUrl(url: string | null | undefined): string | null {
  // التحقق من وجود الرابط
  if (!url || url.trim() === '') {
    return null;
  }

  const cleanUrl = url.trim();

  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (isAbsoluteUrl(cleanUrl)) {
    return cleanUrl;
  }

  // إذا كان الرابط نسبياً، حوله إلى رابط كامل
  return buildAbsoluteUrl(cleanUrl);
}

/**
 * التحقق من كون الرابط مطلقاً (كامل)
 * Check if URL is absolute
 */
function isAbsoluteUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * بناء رابط مطلق من رابط نسبي
 * Build absolute URL from relative URL
 */
function buildAbsoluteUrl(relativePath: string): string {
  const { baseUrl, pathPrefix } = FILE_SERVER_CONFIG;
  
  // تنظيف المسار النسبي
  let cleanPath = relativePath;
  
  // إزالة الشرطة المائلة في البداية إذا وجدت
  if (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }
  
  // إضافة بادئة المجلد إذا كانت موجودة
  if (pathPrefix && pathPrefix.trim() !== '') {
    const cleanPrefix = pathPrefix.trim().replace(/^\/+|\/+$/g, '');
    cleanPath = `${cleanPrefix}/${cleanPath}`;
  }
  
  // بناء الرابط الكامل
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
  return `${cleanBaseUrl}/${cleanPath}`;
}

/**
 * تحويل عدة روابط دفعة واحدة
 * Resolve multiple URLs at once
 */
export function resolveMultipleFileUrls(urls: (string | null | undefined)[]): (string | null)[] {
  return urls.map(url => resolveFileUrl(url));
}

/**
 * تحويل روابط كائن التمرين
 * Resolve exercise object URLs
 */
export function resolveExerciseUrls<T extends { exerciseImageUrl?: string | null; solutionImageUrl?: string | null }>(
  exercise: T
): T {
  return {
    ...exercise,
    exerciseImageUrl: resolveFileUrl(exercise.exerciseImageUrl),
    solutionImageUrl: resolveFileUrl(exercise.solutionImageUrl)
  };
}

/**
 * تحويل روابط مصفوفة من التمارين
 * Resolve URLs for array of exercises
 */
export function resolveExercisesUrls<T extends { exerciseImageUrl?: string | null; solutionImageUrl?: string | null }>(
  exercises: T[]
): T[] {
  return exercises.map(exercise => resolveExerciseUrls(exercise));
}

/**
 * الحصول على إعدادات الخادم الحالية
 * Get current server configuration
 */
export function getFileServerConfig(): FileServerConfig {
  return { ...FILE_SERVER_CONFIG };
}

/**
 * تحديث إعدادات الخادم (للاختبار أو التطوير)
 * Update server configuration (for testing or development)
 */
export function updateFileServerConfig(newConfig: Partial<FileServerConfig>): void {
  Object.assign(FILE_SERVER_CONFIG, newConfig);
}

/**
 * التحقق من صحة رابط الملف
 * Validate file URL
 */
export function validateFileUrl(url: string | null | undefined): boolean {
  const resolvedUrl = resolveFileUrl(url);
  
  if (!resolvedUrl) {
    return false;
  }
  
  try {
    const urlObj = new URL(resolvedUrl);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * الحصول على رابط احتياطي في حالة فشل الرابط الأساسي
 * Get fallback URL if primary URL fails
 */
export function getFallbackUrl(relativePath: string, fallbackIndex: number = 0): string | null {
  const { fallbackDomains, pathPrefix } = FILE_SERVER_CONFIG;
  
  if (fallbackIndex >= fallbackDomains.length) {
    return null;
  }
  
  const fallbackDomain = fallbackDomains[fallbackIndex];
  let cleanPath = relativePath;
  
  if (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }
  
  if (pathPrefix && pathPrefix.trim() !== '') {
    const cleanPrefix = pathPrefix.trim().replace(/^\/+|\/+$/g, '');
    cleanPath = `${cleanPrefix}/${cleanPath}`;
  }
  
  const cleanDomain = fallbackDomain.replace(/\/+$/, '');
  return `${cleanDomain}/${cleanPath}`;
}

/**
 * تسجيل معلومات التكوين (للتطوير)
 * Log configuration info (for development)
 */
export function logFileServerConfig(): void {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 File Server Configuration:', FILE_SERVER_CONFIG);
  }
}
