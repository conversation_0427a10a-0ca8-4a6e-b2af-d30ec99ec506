/**
 * ملف التكوين المركزي للمشروع
 * Central Configuration File for the Project
 * 
 * يحتوي على جميع الإعدادات والمتغيرات المستخدمة في المشروع
 * Contains all settings and variables used throughout the project
 */

// Site Configuration
export const siteConfig = {
  // الرابط الأساسي للموقع - يتم قراءته من متغير البيئة
  // Base URL - read from environment variable
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.talamid.ma',
  
  // اسم الموقع
  // Site name
  name: process.env.NEXT_PUBLIC_SITE_NAME || 'منصة التعليم المغربي',
  nameEn: 'Moroccan Education Platform',
  
  // وصف الموقع
  // Site description
  description: 'منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية',
  descriptionEn: 'Comprehensive educational platform for students in Morocco - interactive lessons and exercises for all educational levels according to Moroccan curricula',
  
  // معلومات الاتصال
  // Contact information
  contact: {
    email: '<EMAIL>',
    phone: '+212-XXX-XXXXXX',
  },
  
  // وسائل التواصل الاجتماعي
  // Social media
  social: {
    twitter: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@MoroccanEducation',
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_PAGE || 'MoroccanEducationPlatform',
  },
  
  // إعدادات SEO
  // SEO settings
  seo: {
    defaultLocale: process.env.NEXT_PUBLIC_DEFAULT_LOCALE || 'ar-MA',
    supportedLocales: (process.env.NEXT_PUBLIC_SUPPORTED_LOCALES || 'ar,ar-MA').split(','),
    keywords: [
      'تعليم مغربي',
      'دروس تفاعلية',
      'تمارين تعليمية',
      'مناهج مغربية',
      'طلاب مغاربة',
      'تعليم ابتدائي المغرب',
      'تعليم إعدادي المغرب',
      'تعليم ثانوي المغرب',
      'باكالوريا مغربية',
      'امتحانات مغربية',
      'ملخصات دروس',
      'واجبات منزلية',
      'تعليم إلكتروني المغرب',
      'منصة تعليمية مغربية',
      'مراجعة دروس',
      'جذع مشترك',
      'أولى باك',
      'ثانية باك'
    ],
  },
  
  // إعدادات التحليلات
  // Analytics settings
  analytics: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    enablePerformanceMonitoring: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true',
  },
  
  // إعدادات الميزات
  // Feature flags
  features: {
    enablePWA: process.env.NEXT_PUBLIC_ENABLE_PWA === 'true',
    enableOfflineMode: process.env.NEXT_PUBLIC_ENABLE_OFFLINE_MODE === 'true',
  },
  
  // إعدادات التحقق
  // Verification settings
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    bing: process.env.BING_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
  },
}

// دوال مساعدة لبناء الروابط
// Helper functions for building URLs
export const buildUrl = (path: string = '') => {
  const baseUrl = siteConfig.url.endsWith('/') ? siteConfig.url.slice(0, -1) : siteConfig.url
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

// دالة لبناء رابط الصورة المشتركة
// Function to build Open Graph image URL
export const buildOgImageUrl = (path: string = '/og-image.png') => {
  return buildUrl(path)
}

// دالة لبناء رابط خريطة الموقع
// Function to build sitemap URL
export const buildSitemapUrl = () => {
  return buildUrl('/sitemap.xml')
}

// دالة لبناء رابط robots.txt
// Function to build robots.txt URL
export const buildRobotsUrl = () => {
  return buildUrl('/robots.txt')
}

// تصدير الرابط الأساسي للاستخدام المباشر
// Export base URL for direct use
export const BASE_URL = siteConfig.url

// تصدير اسم الموقع للاستخدام المباشر
// Export site name for direct use
export const SITE_NAME = siteConfig.name

// تصدير وصف الموقع للاستخدام المباشر
// Export site description for direct use
export const SITE_DESCRIPTION = siteConfig.description

export default siteConfig
