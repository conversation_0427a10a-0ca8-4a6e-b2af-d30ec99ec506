/**
 * Exam filtering utility
 * Defines which educational levels are allowed to display exams
 * Based on user requirements: Sixth grade primary, Third grade middle school, 
 * All first year baccalaureate levels, All second year baccalaureate levels
 */

import type { Year, Subject, Lesson } from '@/data/types';

// Define the allowed year IDs for exam display
export const ALLOWED_EXAM_YEAR_IDS = [
  // Sixth grade primary (سادس ابتدائي)
  'grade6',
  
  // Third grade middle school (ثالث إعدادي)  
  'grade9',
  
  // All first year baccalaureate levels (جميع مستويات أولى باك)
  'first_bac_sciences_math',
  'first_bac_tech_elec', 
  'first_bac_econ_mgmt',
  'first_bac_humanities',
  'first_bac_tech_mech',
  'first_bac_sciences_exp',
  
  // All second year baccalaureate levels (جميع مستويات ثانية باك)
  'second_bac_tech_mech',
  'second_bac_biology',
  'second_bac_math_a',
  'second_bac_math_b', 
  'second_bac_physics',
  'second_bac_accounting',
  'second_bac_agriculture',
  'second_bac_economics',
  'second_bac_humanities',
  'second_bac_literature',
  'second_bac_tech_elec'
];

// Define the allowed level IDs for exam display
export const ALLOWED_EXAM_LEVEL_IDS = [
  'first_bac',
  'second_bac'
];

/**
 * Check if a year ID is allowed to display exams
 */
export function isYearAllowedForExams(yearId: string): boolean {
  return ALLOWED_EXAM_YEAR_IDS.includes(yearId);
}

/**
 * Check if a level ID is allowed to display exams
 */
export function isLevelAllowedForExams(levelId: string): boolean {
  return ALLOWED_EXAM_LEVEL_IDS.includes(levelId) || 
         (levelId === 'primary' && false) || // Only grade6 from primary
         (levelId === 'middle' && false);    // Only grade9 from middle
}

/**
 * Check if a subject is allowed to display exams based on its year
 */
export function isSubjectAllowedForExams(subject: Subject, year?: Year): boolean {
  if (!year) return false;
  return isYearAllowedForExams(year.id);
}

/**
 * Filter lessons to only include exams from allowed educational levels
 */
export function filterExamLessons(lessons: Lesson[], yearId: string): Lesson[] {
  // If the year is not allowed for exams, filter out all exam lessons
  if (!isYearAllowedForExams(yearId)) {
    return lessons.filter(lesson => lesson.content_type !== 'exam');
  }
  
  // If the year is allowed, return all lessons including exams
  return lessons;
}

/**
 * Filter lessons to only return exam lessons from allowed educational levels
 */
export function getFilteredExamLessons(lessons: Lesson[], yearId: string): Lesson[] {
  // Only return exam lessons if the year is allowed
  if (!isYearAllowedForExams(yearId)) {
    return [];
  }
  
  return lessons.filter(lesson => lesson.content_type === 'exam');
}

/**
 * Check if an exam lesson should be accessible based on its educational level
 */
export async function isExamLessonAccessible(
  lessonId: string,
  getLesson: (id: string) => Promise<any>,
  getSubject: (id: string) => Promise<any>,
  getYear: (id: string) => Promise<any>
): Promise<boolean> {
  try {
    const lesson = await getLesson(lessonId);
    if (!lesson || lesson.content_type !== 'exam') {
      return true; // Non-exam lessons are always accessible
    }

    const subject = await getSubject(lesson.subjectId);
    if (!subject) return false;

    const year = await getYear(subject.yearId);
    if (!year) return false;

    return isYearAllowedForExams(year.id);
  } catch (error) {
    console.error('Error checking exam lesson accessibility:', error);
    return false;
  }
}

/**
 * Check if an exam lesson should be accessible based on lesson object directly
 */
export function isExamLessonAccessibleDirect(lesson: Lesson): boolean {
  if (!lesson || lesson.content_type !== 'exam') {
    return true; // Non-exam lessons are always accessible
  }

  // For now, we'll assume all exam lessons are accessible
  // In the future, we can add more complex logic here
  return true;
}
