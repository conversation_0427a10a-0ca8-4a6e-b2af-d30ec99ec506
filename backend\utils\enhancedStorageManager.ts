import { Level, Year, Subject, Lesson } from '@/data/types';
import { cacheUtils } from '@/utils/enhanced-storage';

// مفاتيح التخزين المحسنة
const CACHE_KEYS = {
  LEVELS: 'levels',
  YEARS: 'years',
  SUBJECTS: 'subjects',
  LESSONS: 'lessons',
  YEARS_BY_LEVEL: (levelId: string) => `years_level_${levelId}`,
  SUBJECTS_BY_YEAR: (yearId: string) => `subjects_year_${yearId}`,
  LESSONS_BY_SUBJECT: (subjectId: string) => `lessons_subject_${subjectId}`,
};

// أوقات انتهاء الصلاحية المختلفة (بالدقائق)
const TTL = {
  LONG: 60,    // ساعة واحدة للبيانات الثابتة
  MEDIUM: 30,  // 30 دقيقة للبيانات متوسطة التغيير
  SHORT: 10,   // 10 دقائق للبيانات سريعة التغيير
};

// === وظائف حفظ البيانات ===

export const saveLevels = (levels: Level[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.LEVELS, levels, TTL.LONG);
};

export const saveYears = (years: Year[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.YEARS, years, TTL.LONG);
};

export const saveSubjects = (subjects: Subject[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.SUBJECTS, subjects, TTL.MEDIUM);
};

export const saveLessons = (lessons: Lesson[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.LESSONS, lessons, TTL.SHORT);
};

export const saveYearsByLevel = (levelId: string, years: Year[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.YEARS_BY_LEVEL(levelId), years, TTL.LONG);
};

export const saveSubjectsByYear = (yearId: string, subjects: Subject[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.SUBJECTS_BY_YEAR(yearId), subjects, TTL.MEDIUM);
};

export const saveLessonsBySubject = (subjectId: string, lessons: Lesson[]): boolean => {
  return cacheUtils.setWithTTL(CACHE_KEYS.LESSONS_BY_SUBJECT(subjectId), lessons, TTL.SHORT);
};

// === وظائف استرجاع البيانات ===

export const getLevelsFromStorage = (): Level[] | null => {
  return cacheUtils.get<Level[]>(CACHE_KEYS.LEVELS);
};

export const getYearsFromStorage = (): Year[] | null => {
  return cacheUtils.get<Year[]>(CACHE_KEYS.YEARS);
};

export const getSubjectsFromStorage = (): Subject[] | null => {
  return cacheUtils.get<Subject[]>(CACHE_KEYS.SUBJECTS);
};

export const getLessonsFromStorage = (): Lesson[] | null => {
  return cacheUtils.get<Lesson[]>(CACHE_KEYS.LESSONS);
};

export const getYearsByLevelFromStorage = (levelId: string): Year[] | null => {
  return cacheUtils.get<Year[]>(CACHE_KEYS.YEARS_BY_LEVEL(levelId));
};

export const getSubjectsByYearFromStorage = (yearId: string): Subject[] | null => {
  return cacheUtils.get<Subject[]>(CACHE_KEYS.SUBJECTS_BY_YEAR(yearId));
};

export const getLessonsBySubjectFromStorage = (subjectId: string): Lesson[] | null => {
  return cacheUtils.get<Lesson[]>(CACHE_KEYS.LESSONS_BY_SUBJECT(subjectId));
};

// === وظائف إدارة التخزين ===

export const clearAllData = (): void => {
  cacheUtils.clear();
  console.log('تم مسح جميع البيانات من التخزين المحسن');
};

export const clearSpecificData = (type: 'levels' | 'years' | 'subjects' | 'lessons'): void => {
  switch (type) {
    case 'levels':
      cacheUtils.remove(CACHE_KEYS.LEVELS);
      break;
    case 'years':
      cacheUtils.remove(CACHE_KEYS.YEARS);
      break;
    case 'subjects':
      cacheUtils.remove(CACHE_KEYS.SUBJECTS);
      break;
    case 'lessons':
      cacheUtils.remove(CACHE_KEYS.LESSONS);
      break;
  }
};

export const getStorageStats = () => {
  return cacheUtils.getStats();
};

// === وظائف التحقق من وجود البيانات ===

export const hasLevels = (): boolean => {
  return getLevelsFromStorage() !== null;
};

export const hasYears = (): boolean => {
  return getYearsFromStorage() !== null;
};

export const hasSubjects = (): boolean => {
  return getSubjectsFromStorage() !== null;
};

export const hasLessons = (): boolean => {
  return getLessonsFromStorage() !== null;
};

export const hasYearsForLevel = (levelId: string): boolean => {
  return getYearsByLevelFromStorage(levelId) !== null;
};

export const hasSubjectsForYear = (yearId: string): boolean => {
  return getSubjectsByYearFromStorage(yearId) !== null;
};

export const hasLessonsForSubject = (subjectId: string): boolean => {
  return getLessonsBySubjectFromStorage(subjectId) !== null;
};

// === وظائف التحديث الذكي ===

// تحديث البيانات فقط إذا كانت مختلفة
export const updateLevelsIfChanged = (newLevels: Level[]): boolean => {
  const existingLevels = getLevelsFromStorage();
  if (!existingLevels || JSON.stringify(existingLevels) !== JSON.stringify(newLevels)) {
    return saveLevels(newLevels);
  }
  return false; // لم يتم التحديث لأن البيانات متطابقة
};

export const updateYearsIfChanged = (newYears: Year[]): boolean => {
  const existingYears = getYearsFromStorage();
  if (!existingYears || JSON.stringify(existingYears) !== JSON.stringify(newYears)) {
    return saveYears(newYears);
  }
  return false;
};

export const updateSubjectsIfChanged = (newSubjects: Subject[]): boolean => {
  const existingSubjects = getSubjectsFromStorage();
  if (!existingSubjects || JSON.stringify(existingSubjects) !== JSON.stringify(newSubjects)) {
    return saveSubjects(newSubjects);
  }
  return false;
};

export const updateLessonsIfChanged = (newLessons: Lesson[]): boolean => {
  const existingLessons = getLessonsFromStorage();
  if (!existingLessons || JSON.stringify(existingLessons) !== JSON.stringify(newLessons)) {
    return saveLessons(newLessons);
  }
  return false;
};

// === وظائف التحديث المجمع ===

export const batchUpdate = async (data: {
  levels?: Level[];
  years?: Year[];
  subjects?: Subject[];
  lessons?: Lesson[];
}): Promise<{
  levels: boolean;
  years: boolean;
  subjects: boolean;
  lessons: boolean;
}> => {
  const results = {
    levels: false,
    years: false,
    subjects: false,
    lessons: false,
  };

  if (data.levels) {
    results.levels = updateLevelsIfChanged(data.levels);
  }

  if (data.years) {
    results.years = updateYearsIfChanged(data.years);
  }

  if (data.subjects) {
    results.subjects = updateSubjectsIfChanged(data.subjects);
  }

  if (data.lessons) {
    results.lessons = updateLessonsIfChanged(data.lessons);
  }

  return results;
};

// === وظائف التنظيف الدوري ===

export const performMaintenance = (): void => {
  cacheUtils.cleanup();
  
  const stats = getStorageStats();
  console.log('إحصائيات التخزين بعد التنظيف:', {
    totalItems: stats.totalItems,
    totalSizeKB: Math.round(stats.totalSize / 1024),
    oldestItemAge: Math.round((Date.now() - stats.oldestItem) / 1000 / 60), // بالدقائق
  });
};

// تشغيل التنظيف كل 10 دقائق
if (typeof window !== 'undefined') {
  setInterval(performMaintenance, 10 * 60 * 1000);
}

// تصدير المفاتيح للاستخدام الخارجي
export { CACHE_KEYS, TTL };
