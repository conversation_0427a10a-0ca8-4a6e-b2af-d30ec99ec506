#!/bin/bash

echo "🚀 بدء تحويل المشروع إلى Next.js..."

# إنشاء نسخة احتياطية
echo "📦 إنشاء نسخة احتياطية..."
cp -r src src_backup
cp package.json package_backup.json
cp tsconfig.json tsconfig_backup.json
cp tailwind.config.ts tailwind_backup.config.ts

# نسخ ملفات التكوين الجديدة
echo "⚙️ تحديث ملفات التكوين..."
cp package-nextjs.json package.json
cp tsconfig-nextjs.json tsconfig.json
cp tailwind-nextjs.config.js tailwind.config.js

# إنشاء ملف next-env.d.ts
echo "/// <reference types=\"next\" />" > next-env.d.ts
echo "/// <reference types=\"next/image-types/global\" />" >> next-env.d.ts

# نسخ ملفات backend
echo "🔧 نسخ ملفات Backend..."
mkdir -p backend/api
mkdir -p backend/data
mkdir -p backend/utils

# نسخ ملفات integrations
echo "🔗 نسخ ملفات Integrations..."
cp -r src/integrations integrations

# نسخ ملفات utils
echo "🛠️ نسخ ملفات Utils..."
cp -r src/utils utils

# إنشاء ملف .env.local إذا لم يكن موجوداً
if [ ! -f .env.local ]; then
    echo "🔐 إنشاء ملف .env.local..."
    cat > .env.local << EOL
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
EOL
fi

# إنشاء ملف .gitignore محدث
echo "📝 تحديث .gitignore..."
cat > .gitignore << EOL
# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Backup files
src_backup/
*_backup.*
EOL

echo "✅ تم إنشاء الملفات الأساسية لـ Next.js!"
echo ""
echo "📋 الخطوات التالية:"
echo "1. تشغيل: npm install"
echo "2. تحديث متغيرات البيئة في .env.local"
echo "3. نسخ الملفات المتبقية يدوياً (راجع NEXTJS_MIGRATION_README.md)"
echo "4. تشغيل: npm run dev"
echo ""
echo "📖 راجع ملف NEXTJS_MIGRATION_README.md للتفاصيل الكاملة"
