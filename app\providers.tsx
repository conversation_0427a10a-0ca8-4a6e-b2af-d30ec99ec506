'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { TooltipProvider } from '@/components/ui/tooltip'
import { Toaster } from '@/components/ui/toaster'
import { Toaster as Sonner } from '@/components/ui/sonner'
import { ThemeProvider } from '@/context/ThemeContext'
import { useState } from 'react'

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // البيانات تبقى صالحة لمدة 5 دقائق
        staleTime: 5 * 60 * 1000,
        // البيانات تبقى في الذاكرة لمدة 10 دقائق
        gcTime: 10 * 60 * 1000,
        // إعادة جلب البيانات عند التركيز على النافذة
        refetchOnWindowFocus: false,
        // إعادة جلب البيانات عند إعادة الاتصال
        refetchOnReconnect: true,
        // عدد المحاولات عند فشل الطلب
        retry: 2,
        // تأخير بين المحاولات
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      mutations: {
        // عدد المحاولات للطفرات
        retry: 1,
      },
    },
  }))

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <ThemeProvider>
          {children}
          <Toaster />
          <Sonner />
        </ThemeProvider>
      </TooltipProvider>
    </QueryClientProvider>
  )
}
