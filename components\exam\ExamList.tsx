'use client';

import TableContentList from '@/components/ui/table-content-list';
import { Exercise } from '@/data/types';

interface ExamListProps {
  exercises: Exercise[];
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

const ExamList = ({
  exercises,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: ExamListProps) => {
  return (
    <TableContentList
      exercises={exercises}
      contentType="exam"
      lessonTitle={lessonTitle}
      subjectName={subjectName}
      yearName={yearName}
      levelName={levelName}
    />
  );
};

export default ExamList;
