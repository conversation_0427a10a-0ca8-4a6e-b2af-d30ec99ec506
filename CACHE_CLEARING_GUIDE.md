# دليل مسح التخزين المؤقت لإظهار حقل الوصف
# Cache Clearing Guide to Show Description Field

## المشكلة / Problem
حقل `description` الجديد لا يظهر في الموقع رغم إضافته إلى قاعدة البيانات وتحديث الكود.

The new `description` field doesn't show on the website despite being added to the database and code updates.

## السبب / Cause
البيانات محفوظة في التخزين المؤقت (localStorage + memory cache) ولا تحتوي على حقل `description` الجديد.

Data is cached in localStorage + memory cache and doesn't contain the new `description` field.

## الحلول / Solutions

### 🚀 الحل السريع / Quick Solution

#### الطريقة 1: استخدام وحدة تحكم المتصفح / Browser Console
1. افتح الموقع في المتصفح
2. اضغط F12 لفتح أدوات المطور
3. اذه<PERSON> إلى تبويب Console
4. اكتب إحدى هذه الأوامر:

```javascript
// مسح التخزين المؤقت وإعادة التحميل
clearEducationCache()

// أو
forceFreshData()

// للتحقق من حالة التخزين المؤقت
checkCacheStatus()
```

#### الطريقة 2: استخدام زر "تحديث البيانات" في Footer
1. اذهب إلى أي صفحة في الموقع
2. انزل إلى أسفل الصفحة (Footer)
3. اضغط على زر "تحديث البيانات"
4. ستظهر رسالة تأكيد وسيتم إعادة تحميل الصفحة تلقائياً

#### الطريقة 3: مسح يدوي / Manual Clearing
1. افتح أدوات المطور (F12)
2. اذهب إلى تبويب Application/Storage
3. في الجانب الأيسر، اختر Local Storage
4. احذف جميع المفاتيح التي تحتوي على:
   - `arab-edu`
   - `cache_`
   - `levels`
   - `years`
   - `subjects`
   - `lessons`
5. أعد تحميل الصفحة

### 🔧 للمطورين / For Developers

#### إضافة دالة مسح التخزين المؤقت في الكود
```typescript
import { clearAllCacheAndRefresh } from '@/backend/utils/dataLoader';

// استخدام الدالة
clearAllCacheAndRefresh();
```

#### التحقق من البيانات الجديدة
```typescript
import { getYear } from '@/data/educationData';

// اختبار جلب سنة معينة
const year = await getYear('first_bac_sciences_math');
console.log(year); // يجب أن يحتوي على حقل description
```

## التحقق من نجاح الحل / Verification

بعد مسح التخزين المؤقت، يجب أن ترى:

1. **في وحدة التحكم:**
   ```
   جاري جلب السنوات الدراسية من Supabase...
   تم جلب X سنوات دراسية من Supabase
   ```

2. **في صفحة السنة:**
   - وصف مناسب تحت عنوان السنة
   - مثال: "السنة الثانية من التعليم الثانوي - تخصص في الرياضيات والعلوم الدقيقة"

3. **في أدوات المطور:**
   - البيانات المُجلبة تحتوي على حقل `description`

## أمثلة على الأوصاف المتوقعة / Expected Descriptions

- **الأولى ابتدائي**: "السنة الأولى من التعليم الابتدائي - بداية الرحلة التعليمية وتعلم الأساسيات"
- **العلوم الرياضية**: "السنة الثانية من التعليم الثانوي - تخصص في الرياضيات والعلوم الدقيقة"
- **الثانية باك فيزياء**: "السنة الأخيرة من التعليم الثانوي - إتقان الفيزياء والكيمياء والتحضير للبكالوريا"

## استكشاف الأخطاء / Troubleshooting

### إذا لم يظهر الوصف بعد مسح التخزين المؤقت:

1. **تحقق من قاعدة البيانات:**
   ```sql
   SELECT id, name, description FROM public.years LIMIT 5;
   ```

2. **تحقق من دالة التحميل:**
   - تأكد من أن `supabaseLoader.ts` يحتوي على حقل `description`
   - تأكد من أن `Year` interface يحتوي على `description: string`

3. **تحقق من الشبكة:**
   - افتح Network tab في أدوات المطور
   - ابحث عن طلبات Supabase
   - تأكد من أن البيانات المُستلمة تحتوي على `description`

4. **إعادة تشغيل الخادم:**
   ```bash
   npm run dev
   ```

## ملاحظات مهمة / Important Notes

- ✅ هذا الإجراء آمن ولن يؤثر على البيانات في قاعدة البيانات
- ✅ البيانات ستُجلب من Supabase مرة أخرى
- ✅ التخزين المؤقت سيُعاد بناؤه تلقائياً
- ⚠️ قد يكون التحميل أبطأ قليلاً في المرة الأولى بعد المسح

## للمستقبل / For Future

لتجنب هذه المشكلة في المستقبل:
1. امسح التخزين المؤقت بعد أي تحديث في قاعدة البيانات
2. استخدم versioning للتخزين المؤقت
3. أضف آلية للتحقق من تحديثات البيانات
