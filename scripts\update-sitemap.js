#!/usr/bin/env node

/**
 * Script لتحديث sitemap.xml تلقائياً
 * يمكن تشغيله يدوياً أو من خلال cron job
 */

const https = require('https');
const http = require('http');

// إعدادات الموقع
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.talamid.ma';
const REVALIDATE_TOKEN = process.env.REVALIDATE_TOKEN || 'talamid_ma_secure_token_2025';

/**
 * دالة لإرسال طلب POST لتحديث sitemap
 */
function updateSitemap() {
  return new Promise((resolve, reject) => {
    const url = new URL('/api/revalidate-sitemap', SITE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const postData = JSON.stringify({
      action: 'revalidate',
      timestamp: new Date().toISOString()
    });

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${REVALIDATE_TOKEN}`
      }
    };

    const req = client.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ تم تحديث sitemap بنجاح:', response.message);
            resolve(response);
          } else {
            console.error('❌ فشل في تحديث sitemap:', response.error);
            reject(new Error(response.error));
          }
        } catch (error) {
          console.error('❌ خطأ في تحليل الاستجابة:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ خطأ في الشبكة:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * دالة للتحقق من حالة الموقع
 */
function checkSiteHealth() {
  return new Promise((resolve, reject) => {
    const url = new URL('/api/health', SITE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'GET',
      timeout: 10000
    };

    const req = client.request(options, (res) => {
      if (res.statusCode === 200) {
        console.log('✅ الموقع يعمل بشكل طبيعي');
        resolve(true);
      } else {
        console.warn(`⚠️ الموقع يستجيب بكود: ${res.statusCode}`);
        resolve(false);
      }
    });

    req.on('error', (error) => {
      console.error('❌ الموقع غير متاح:', error.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.error('❌ انتهت مهلة الاتصال بالموقع');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء تحديث sitemap...');
  console.log(`📍 الموقع: ${SITE_URL}`);
  
  try {
    // التحقق من حالة الموقع أولاً
    const isHealthy = await checkSiteHealth();
    
    if (!isHealthy) {
      console.error('❌ الموقع غير متاح، لا يمكن تحديث sitemap');
      process.exit(1);
    }

    // تحديث sitemap
    await updateSitemap();
    
    console.log('🎉 تم تحديث sitemap بنجاح!');
    console.log(`🔗 يمكنك مراجعة sitemap على: ${SITE_URL}/sitemap.xml`);
    
  } catch (error) {
    console.error('❌ فشل في تحديث sitemap:', error.message);
    process.exit(1);
  }
}

// تشغيل الدالة الرئيسية إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  main();
}

module.exports = {
  updateSitemap,
  checkSiteHealth
};
