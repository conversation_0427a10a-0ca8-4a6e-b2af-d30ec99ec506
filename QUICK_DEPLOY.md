# 🚀 دليل النشر السريع على cPanel

## ✅ التحقق من الجاهزية
```bash
node check-setup.js
```

## 📦 التحضير للنشر

### 1. بناء المشروع
```bash
npm run build
```

### 2. تثبيت التبعيات الإضافية
```bash
npm install compression express helmet
```

### 3. إنشاء ملف البيئة
```bash
cp .env.example .env.local
# ثم قم بتحرير .env.local وإضافة بيانات Supabase الخاصة بك
```

## 🌐 النشر على cPanel

### الطريقة الأولى: رفع مباشر
1. **ضغط الملفات**:
   ```bash
   tar -czf deploy.tar.gz \
     .next/ \
     public/ \
     node_modules/ \
     package.json \
     package-lock.json \
     server.js \
     next.config.js \
     .env.local
   ```

2. **رفع إلى cPanel**:
   - اذهب إلى File Manager
   - ارفع `deploy.tar.gz`
   - استخرج الملفات

### الطريقة الثانية: Git (إذا كان متاحاً)
```bash
git add .
git commit -m "Ready for cPanel deployment"
git push origin main
```

## ⚙️ إعداد Node.js App في cPanel

1. **إنشاء التطبيق**:
   - اذهب إلى "Node.js App"
   - اضغط "Create App"
   - املأ البيانات:
     - **Node.js version**: 18.17.0+
     - **Application mode**: Production
     - **Application startup file**: `server.js`
     - **Application root**: مسار مجلد التطبيق

2. **متغيرات البيئة**:
   ```
   NODE_ENV=production
   PORT=3000
   NEXT_PUBLIC_SUPABASE_URL=your_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key_here
   ```

3. **بدء التطبيق**:
   - اضغط "Start App"

## 🔧 التشغيل اليدوي (عبر Terminal)

```bash
# الانتقال إلى مجلد التطبيق
cd /path/to/your/app

# تثبيت التبعيات
npm install --production

# بدء التطبيق
npm start

# أو استخدام السكريبت
./start.sh
```

## 🔍 التحقق من التشغيل

```bash
# فحص حالة التطبيق
curl http://yourdomain.com/health

# فحص الصفحة الرئيسية
curl http://yourdomain.com
```

## 🐛 حل المشاكل الشائعة

### خطأ "Cannot find module"
```bash
rm -rf node_modules package-lock.json
npm install
```

### خطأ في المنفذ
- تأكد من أن المنفذ 3000 متاح
- أو غيّر `PORT` في متغيرات البيئة

### مشكلة Supabase
- تحقق من صحة URL و ANON_KEY
- تأكد من إعدادات CORS في Supabase

## 📞 الدعم

- **الدليل الكامل**: `CPANEL_DEPLOYMENT.md`
- **فحص الإعداد**: `node check-setup.js`
- **بدء التشغيل**: `./start.sh`

---
**نصيحة**: احتفظ بنسخة احتياطية قبل أي تحديث!
