import { Suspense } from 'react'
import { Metadata } from 'next'
import YearClient from './year-client'
import { getYearServerSide, getLevelServerSide } from '@/backend/api/educationAPI'
import { generateYearMetadata } from '@/lib/seo'
import { decodeIdFromUrl } from '@/utils/id-utils'

type Props = {
  params: { yearId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const yearId = decodeIdFromUrl(params.yearId);
    const year = await getYearServerSide(yearId)
    if (!year) {
      return {
        title: 'السنة الدراسية غير موجودة - منصة التعليم العربي',
        description: 'لم يتم العثور على السنة الدراسية المطلوبة',
        robots: { index: false, follow: false }
      }
    }

    const level = await getLevelServerSide(year.levelId)
    return generateYearMetadata(year, level)
  } catch (error) {
    console.error('Error generating year metadata:', error)
    return {
      title: 'السنة الدراسية - منصة التعليم العربي',
      description: 'استكشف المواد الدراسية للسنة الدراسية',
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function YearPage({ params }: Props) {
  const yearId = decodeIdFromUrl(params.yearId);

  return (
    <Suspense fallback={<PageLoader />}>
      <YearClient yearId={yearId} />
    </Suspense>
  )
}
