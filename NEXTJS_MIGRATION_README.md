# تحويل المشروع إلى Next.js

## الملفات التي تم إنشاؤها ✅

### ملفات التكوين الأساسية ✅
- `package-nextjs.json` - ملف package.json محدث لـ Next.js ✅
- `next.config.js` - تكوين Next.js ✅
- `tsconfig-nextjs.json` - تكوين TypeScript لـ Next.js ✅
- `tailwind-nextjs.config.js` - تكوين Tailwind CSS لـ Next.js ✅
- `postcss-nextjs.config.js` - تكوين PostCSS لـ Next.js ✅
- `components-nextjs.json` - تكوين ShadCN UI لـ Next.js ✅

### بنية التطبيق ✅
- `app/layout.tsx` - Layout الرئيسي ✅
- `app/providers.tsx` - مزودي الخدمات ✅
- `app/globals.css` - ملف CSS الرئيسي ✅
- `app/page.tsx` - الصفحة الرئيسية (تحويل إلى /levels) ✅

### الصفحات ✅
- `app/levels/page.tsx` - صفحة المستويات ✅
- `app/levels/levels-client.tsx` - مكون العميل للمستويات ✅

### المكونات والمرافق ✅
- `lib/utils.ts` - مرافق مشتركة ✅
- `components/ui/button.tsx` - مكون Button ✅
- `components/ui/card.tsx` - مكون Card ✅
- `components/ui/toast.tsx` - مكون Toast ✅
- `components/ui/toaster.tsx` - مكون Toaster ✅
- `components/ui/tooltip.tsx` - مكون Tooltip ✅
- `components/ui/sonner.tsx` - مكون Sonner ✅
- `components/Header.tsx` - مكون Header ✅
- `components/Footer.tsx` - مكون Footer ✅
- `hooks/use-toast.ts` - Toast hook ✅
- `hooks/use-mobile.tsx` - Mobile detection hook ✅
- `context/ThemeContext.tsx` - سياق الثيم ✅
- `data/types.ts` - أنواع البيانات ✅
- `data/educationData.ts` - بيانات التعليم ✅

### ملفات Backend ✅
- `backend/api/educationAPI.ts` - API التعليم ✅
- `backend/utils/dataLoader.ts` - محمل البيانات ✅
- `backend/utils/supabaseLoader.ts` - محمل Supabase ✅
- `backend/utils/storageManager.ts` - مدير التخزين ✅

### ملفات Integrations ✅
- `integrations/supabase/client.ts` - عميل Supabase ✅
- `integrations/supabase/types.ts` - أنواع Supabase ✅

### ملفات المساعدة ✅
- `migrate-to-nextjs.sh` - سكريبت التحويل ✅
- `NEXTJS_MIGRATION_README.md` - دليل التحويل ✅

## الخطوات المطلوبة لإكمال التحويل

### 1. تثبيت التبعيات
```bash
# نسخ package.json الجديد
cp package-nextjs.json package.json

# تثبيت التبعيات
npm install
```

### 2. تحديث ملفات التكوين
```bash
# نسخ ملفات التكوين
cp tsconfig-nextjs.json tsconfig.json
cp tailwind-nextjs.config.js tailwind.config.js
```

### 3. نسخ الملفات المتبقية
يجب نسخ الملفات التالية من src/ إلى المجلدات الجديدة:

#### مكونات Header
- `src/components/DesktopHeader.tsx` → `components/DesktopHeader.tsx`
- `src/components/MobileHeader.tsx` → `components/MobileHeader.tsx`
- `src/components/HeaderDropdown.tsx` → `components/HeaderDropdown.tsx`

#### مكونات UI المتبقية
- جميع ملفات `src/components/ui/` → `components/ui/`

#### ملفات Backend
- `src/backend/api/educationAPI.ts` → `backend/api/educationAPI.ts`
- `src/backend/utils/supabaseLoader.ts` → `backend/utils/supabaseLoader.ts`
- `src/backend/utils/storageManager.ts` → `backend/utils/storageManager.ts`
- `src/backend/data/` → `backend/data/`

#### ملفات Integrations
- `src/integrations/` → `integrations/`

#### الصفحات المتبقية
- إنشاء صفحات Next.js للمسارات:
  - `app/year/[yearId]/page.tsx`
  - `app/subject/[subjectId]/page.tsx`
  - `app/lesson/[lessonId]/page.tsx`
  - `app/homework/[lessonId]/page.tsx` (الفروض)
  - `app/summary/[lessonId]/page.tsx`
  - `app/exam/[lessonId]/page.tsx`
  - `app/about/page.tsx`

### 4. تحديث الاستيرادات
- تحديث جميع استيرادات React Router إلى Next.js navigation
- تحديث مسارات الملفات للتوافق مع بنية Next.js

### 5. إعداد متغيرات البيئة
إنشاء ملف `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 6. تشغيل المشروع
```bash
npm run dev
```

## الاختلافات الرئيسية

### التوجيه
- **قبل**: React Router (`<Link to="/path">`)
- **بعد**: Next.js (`<Link href="/path">`)

### بنية الملفات
- **قبل**: `src/pages/` مع React Router
- **بعد**: `app/` مع App Router

### مكونات العميل
- جميع المكونات التفاعلية تحتاج `'use client'` في الأعلى

### الصور والملفات الثابتة
- **قبل**: `public/` مع Vite
- **بعد**: `public/` مع Next.js (نفس البنية)

## الميزات الجديدة في Next.js

1. **Server-Side Rendering (SSR)** - تحسين SEO والأداء
2. **Static Site Generation (SSG)** - صفحات ثابتة سريعة
3. **Image Optimization** - تحسين الصور تلقائياً
4. **Built-in CSS Support** - دعم أفضل لـ CSS
5. **API Routes** - إمكانية إنشاء API endpoints
6. **Automatic Code Splitting** - تقسيم الكود تلقائياً

## ملاحظات مهمة

1. تأكد من تحديث جميع الاستيرادات
2. اختبر جميع الصفحات والمكونات
3. تحقق من عمل Supabase integration
4. اختبر الـ responsive design
5. تأكد من عمل الـ dark mode

## الخطوات التالية

بعد إكمال التحويل الأساسي، يمكن إضافة:
- Server Components لتحسين الأداء
- Metadata API لتحسين SEO
- Middleware للمصادقة
- API Routes للعمليات الخلفية
