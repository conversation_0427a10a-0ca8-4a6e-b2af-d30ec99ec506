-- إضافة حقل العنوان المخصص لجداول المحتوى
-- Add custom title field to content tables

-- ===== إضافة حقل title لجدول التمارين =====
-- Add title field to exercises table
ALTER TABLE public.exercises 
ADD COLUMN IF NOT EXISTS title TEXT;

-- إضافة تعليق على الحقل الجديد
COMMENT ON COLUMN public.exercises.title IS 'العنوان المخصص للتمرين - اختياري';

-- ===== إضافة حقل title لجدول الفروض =====
-- Add title field to homeworks table  
ALTER TABLE public.homeworks 
ADD COLUMN IF NOT EXISTS title TEXT;

-- إضافة تعليق على الحقل الجديد
COMMENT ON COLUMN public.homeworks.title IS 'العنوان المخصص للفرض - اختياري';

-- ===== إضافة حقل title لجدول الامتحانات =====
-- Add title field to exams table
ALTER TABLE public.exams 
ADD COLUMN IF NOT EXISTS title TEXT;

-- إضافة تعليق على الحقل الجديد
COMMENT ON COLUMN public.exams.title IS 'العنوان المخصص للامتحان - اختياري';

-- ===== إضافة حقل title لجدول الملخصات =====
-- Add title field to summaries table
ALTER TABLE public.summaries 
ADD COLUMN IF NOT EXISTS title TEXT;

-- إضافة تعليق على الحقل الجديد
COMMENT ON COLUMN public.summaries.title IS 'العنوان المخصص للملخص - اختياري';

-- ===== إنشاء فهارس للبحث السريع =====
-- Create indexes for fast searching

-- فهرس للبحث في عناوين التمارين
CREATE INDEX IF NOT EXISTS exercises_title_idx ON public.exercises USING gin(to_tsvector('arabic', title));

-- فهرس للبحث في عناوين الفروض
CREATE INDEX IF NOT EXISTS homeworks_title_idx ON public.homeworks USING gin(to_tsvector('arabic', title));

-- فهرس للبحث في عناوين الامتحانات
CREATE INDEX IF NOT EXISTS exams_title_idx ON public.exams USING gin(to_tsvector('arabic', title));

-- فهرس للبحث في عناوين الملخصات
CREATE INDEX IF NOT EXISTS summaries_title_idx ON public.summaries USING gin(to_tsvector('arabic', title));

-- ===== أمثلة على إضافة عناوين مخصصة =====
-- Examples of adding custom titles

-- مثال 1: تحديث تمرين موجود بعنوان مخصص
-- Example 1: Update existing exercise with custom title
/*
UPDATE public.exercises 
SET title = 'حل المعادلات من الدرجة الثانية'
WHERE id = 'exercise_id_here';
*/

-- مثال 2: إضافة تمرين جديد بعنوان مخصص
-- Example 2: Insert new exercise with custom title
/*
INSERT INTO public.exercises (
    id, 
    lesson_id, 
    title,
    exercise_image_url, 
    solution_image_url,
    hint
) VALUES (
    'new_exercise_id',
    'lesson_id_here', 
    'قوانين نيوتن في الحركة',
    'files/physics/newton_laws.pdf',
    'files/physics/newton_laws_solution.pdf',
    'راجع قوانين الحركة الأساسية'
);
*/

-- مثال 3: إضافة فرض بعنوان مخصص
-- Example 3: Insert homework with custom title
/*
INSERT INTO public.homeworks (
    id, 
    lesson_id, 
    title,
    exercise_image_url, 
    solution_image_url
) VALUES (
    'homework_id',
    'lesson_id_here', 
    'تطبيقات على نظرية فيثاغورس',
    'files/math/pythagoras_homework.pdf',
    'files/math/pythagoras_homework_solution.pdf'
);
*/

-- ===== دوال مساعدة للبحث =====
-- Helper functions for searching

-- دالة البحث في العناوين المخصصة
CREATE OR REPLACE FUNCTION search_content_by_title(search_term TEXT)
RETURNS TABLE(
    content_type TEXT,
    content_id TEXT,
    title TEXT,
    lesson_id TEXT
) AS $$
BEGIN
    RETURN QUERY
    -- البحث في التمارين
    SELECT 'exercise'::TEXT, e.id, e.title, e.lesson_id
    FROM public.exercises e
    WHERE e.title ILIKE '%' || search_term || '%'
    
    UNION ALL
    
    -- البحث في الفروض
    SELECT 'homework'::TEXT, h.id, h.title, h.lesson_id
    FROM public.homeworks h
    WHERE h.title ILIKE '%' || search_term || '%'
    
    UNION ALL
    
    -- البحث في الامتحانات
    SELECT 'exam'::TEXT, ex.id, ex.title, ex.lesson_id
    FROM public.exams ex
    WHERE ex.title ILIKE '%' || search_term || '%'
    
    UNION ALL
    
    -- البحث في الملخصات
    SELECT 'summary'::TEXT, s.id, s.title, s.lesson_id
    FROM public.summaries s
    WHERE s.title ILIKE '%' || search_term || '%';
END;
$$ LANGUAGE plpgsql;

-- ===== إحصائيات المحتوى =====
-- Content statistics

-- دالة لحساب إحصائيات العناوين المخصصة
CREATE OR REPLACE FUNCTION get_custom_titles_stats()
RETURNS TABLE(
    content_type TEXT,
    total_count BIGINT,
    with_custom_title BIGINT,
    without_custom_title BIGINT,
    percentage_with_title NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    -- إحصائيات التمارين
    SELECT 
        'exercises'::TEXT,
        COUNT(*) as total_count,
        COUNT(title) as with_custom_title,
        COUNT(*) - COUNT(title) as without_custom_title,
        ROUND((COUNT(title)::NUMERIC / COUNT(*)) * 100, 2) as percentage_with_title
    FROM public.exercises
    
    UNION ALL
    
    -- إحصائيات الفروض
    SELECT 
        'homeworks'::TEXT,
        COUNT(*) as total_count,
        COUNT(title) as with_custom_title,
        COUNT(*) - COUNT(title) as without_custom_title,
        ROUND((COUNT(title)::NUMERIC / COUNT(*)) * 100, 2) as percentage_with_title
    FROM public.homeworks
    
    UNION ALL
    
    -- إحصائيات الامتحانات
    SELECT 
        'exams'::TEXT,
        COUNT(*) as total_count,
        COUNT(title) as with_custom_title,
        COUNT(*) - COUNT(title) as without_custom_title,
        ROUND((COUNT(title)::NUMERIC / COUNT(*)) * 100, 2) as percentage_with_title
    FROM public.exams
    
    UNION ALL
    
    -- إحصائيات الملخصات
    SELECT 
        'summaries'::TEXT,
        COUNT(*) as total_count,
        COUNT(title) as with_custom_title,
        COUNT(*) - COUNT(title) as without_custom_title,
        ROUND((COUNT(title)::NUMERIC / COUNT(*)) * 100, 2) as percentage_with_title
    FROM public.summaries;
END;
$$ LANGUAGE plpgsql;

-- ===== التحقق من نجاح التحديث =====
-- Verify successful update

-- عرض هيكل الجداول المحدثة
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('exercises', 'homeworks', 'exams', 'summaries')
    AND column_name = 'title'
    AND table_schema = 'public'
ORDER BY table_name;

-- عرض إحصائيات العناوين المخصصة
SELECT * FROM get_custom_titles_stats();

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '✅ تم إضافة حقل العنوان المخصص بنجاح لجميع جداول المحتوى';
    RAISE NOTICE '📊 يمكنك الآن استخدام دالة get_custom_titles_stats() لعرض الإحصائيات';
    RAISE NOTICE '🔍 يمكنك استخدام دالة search_content_by_title(''كلمة البحث'') للبحث في العناوين';
END $$;
